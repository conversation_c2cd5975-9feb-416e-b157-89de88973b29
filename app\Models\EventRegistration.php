<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EventRegistration extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'live_event_id',
        'attended',
        'registered_at',
        'reminder_sent',
    ];

    protected $casts = [
        'attended' => 'boolean',
        'reminder_sent' => 'boolean',
        'registered_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function liveEvent()
    {
        return $this->belongsTo(LiveEvent::class);
    }

    /**
     * Scopes
     */
    public function scopeAttended($query)
    {
        return $query->where('attended', true);
    }

    public function scopeNotAttended($query)
    {
        return $query->where('attended', false);
    }

    /**
     * Accessors
     */
    public function getStatusAttribute()
    {
        if ($this->attended) {
            return 'Attended';
        }

        if ($this->liveEvent->is_past) {
            return 'No Show';
        }

        return 'Registered';
    }

    public function getStatusBadgeClassAttribute()
    {
        if ($this->attended) {
            return 'badge-success';
        }

        if ($this->liveEvent->is_past) {
            return 'badge-warning';
        }

        return 'badge-primary';
    }
}
