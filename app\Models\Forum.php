<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Forum extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'category_id',
        'sort_order',
        'is_active',
        'requires_subscription',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'requires_subscription' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function posts()
    {
        return $this->hasMany(ForumPost::class);
    }

    public function latestPost()
    {
        return $this->hasOne(ForumPost::class)->latest();
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Accessors
     */
    public function getPostsCountAttribute()
    {
        return $this->posts()->count();
    }

    public function getRepliesCountAttribute()
    {
        return ForumPostReply::whereIn('forum_post_id', $this->posts()->pluck('id'))->count();
    }

    public function getLatestActivityAttribute()
    {
        $latestPost = $this->posts()->latest()->first();
        $latestReply = ForumPostReply::whereIn('forum_post_id', $this->posts()->pluck('id'))
                                   ->latest()
                                   ->first();

        if (!$latestPost && !$latestReply) {
            return null;
        }

        if (!$latestReply) {
            return $latestPost->created_at;
        }

        if (!$latestPost) {
            return $latestReply->created_at;
        }

        return $latestPost->created_at->gt($latestReply->created_at)
            ? $latestPost->created_at
            : $latestReply->created_at;
    }

    /**
     * Check if user can access this forum
     */
    public function canAccess($user = null)
    {
        $user = $user ?? auth()->user();

        if (!$this->is_active) {
            return false;
        }

        if (!$user) {
            return !$this->requires_subscription;
        }

        if ($this->requires_subscription && !$user->subscribed('default')) {
            return false;
        }

        return true;
    }
}
