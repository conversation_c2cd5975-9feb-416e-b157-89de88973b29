@extends('layouts.app')

@section('title', 'Payment Successful')

@section('content')
<style>
    .success-container {
        max-width: 600px;
        margin: 4rem auto;
        padding: 3rem;
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        text-align: center;
    }

    .success-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #28a745, #20c997);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        font-size: 3rem;
        color: white;
        animation: successPulse 2s infinite;
    }

    @keyframes successPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .success-title {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .success-message {
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .success-details {
        background: var(--light-bg);
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: left;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e1e5e9;
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 500;
        color: var(--primary-color);
    }

    .detail-value {
        color: #666;
    }

    .next-steps {
        background: linear-gradient(135deg, var(--secondary-color), #e67e22);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }

    .next-steps h3 {
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }

    .next-steps ul {
        text-align: left;
        margin-bottom: 1.5rem;
    }

    .next-steps li {
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .next-steps i {
        color: rgba(255,255,255,0.8);
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-large {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.3s;
    }

    .btn-primary-large {
        background: var(--secondary-color);
        color: white;
        border: none;
    }

    .btn-primary-large:hover {
        background: #e67e22;
        color: white;
    }

    .btn-secondary-large {
        background: transparent;
        color: var(--primary-color);
        border: 2px solid var(--primary-color);
    }

    .btn-secondary-large:hover {
        background: var(--primary-color);
        color: white;
    }

    .confetti {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
    }

    @media (max-width: 768px) {
        .success-container {
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .detail-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>

<div class="container">
    <div class="success-container">
        <!-- Success Icon -->
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>

        <!-- Success Message -->
        <h1 class="success-title">Welcome to The Real World!</h1>
        <p class="success-message">
            Your payment has been processed successfully. You now have full access to all our premium content, 
            live mentorship calls, and exclusive community.
        </p>

        <!-- Payment Details -->
        <div class="success-details">
            <div class="detail-row">
                <span class="detail-label">Subscription Plan</span>
                <span class="detail-value">{{ session('subscription_plan', 'Premium Plan') }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Billing Cycle</span>
                <span class="detail-value">{{ session('billing_cycle', 'Monthly') }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Amount Paid</span>
                <span class="detail-value">${{ session('amount_paid', '49.00') }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Next Billing Date</span>
                <span class="detail-value">{{ session('next_billing', now()->addMonth()->format('M j, Y')) }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Payment Method</span>
                <span class="detail-value">**** **** **** {{ session('last_four', '****') }}</span>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="next-steps">
            <h3>What's Next?</h3>
            <ul>
                <li>
                    <i class="fas fa-check"></i>
                    <span>Access your member dashboard to start learning</span>
                </li>
                <li>
                    <i class="fas fa-check"></i>
                    <span>Join our exclusive community chat</span>
                </li>
                <li>
                    <i class="fas fa-check"></i>
                    <span>Attend your first live mentorship call</span>
                </li>
                <li>
                    <i class="fas fa-check"></i>
                    <span>Download our mobile app for learning on-the-go</span>
                </li>
            </ul>
            <p style="margin: 0; opacity: 0.9;">
                A confirmation email has been sent to your inbox with all the details.
            </p>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{{ route('dashboard') }}" class="btn-large btn-primary-large">
                <i class="fas fa-tachometer-alt"></i> Go to Dashboard
            </a>
            <a href="{{ route('subscriptions.index') }}" class="btn-large btn-secondary-large">
                <i class="fas fa-cog"></i> Manage Subscription
            </a>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Simple confetti effect
    function createConfetti() {
        const colors = ['#f39c12', '#e74c3c', '#3498db', '#2ecc71', '#9b59b6'];
        const confettiContainer = document.createElement('div');
        confettiContainer.className = 'confetti';
        document.body.appendChild(confettiContainer);

        for (let i = 0; i < 50; i++) {
            const confettiPiece = document.createElement('div');
            confettiPiece.style.position = 'absolute';
            confettiPiece.style.width = '10px';
            confettiPiece.style.height = '10px';
            confettiPiece.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confettiPiece.style.left = Math.random() * 100 + '%';
            confettiPiece.style.top = '-10px';
            confettiPiece.style.borderRadius = '50%';
            confettiPiece.style.animation = `fall ${Math.random() * 3 + 2}s linear forwards`;
            confettiContainer.appendChild(confettiPiece);
        }

        // Remove confetti after animation
        setTimeout(() => {
            document.body.removeChild(confettiContainer);
        }, 5000);
    }

    // Add CSS for falling animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fall {
            to {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    // Trigger confetti on page load
    window.addEventListener('load', createConfetti);
</script>
@endpush
@endsection
