<?php $__env->startSection('title', $course->title); ?>

<?php $__env->startSection('content'); ?>
<style>
    .course-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
        color: white;
        padding: 3rem 0;
    }

    .course-hero-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 3rem;
        align-items: center;
    }

    .course-info h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .course-meta {
        display: flex;
        gap: 2rem;
        margin-bottom: 1.5rem;
        font-size: 1rem;
    }

    .course-meta span {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        opacity: 0.9;
    }

    .course-description {
        font-size: 1.1rem;
        line-height: 1.6;
        opacity: 0.9;
        margin-bottom: 2rem;
    }

    .course-stats {
        display: flex;
        gap: 2rem;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        display: block;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .course-video {
        background: rgba(255,255,255,0.1);
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
    }

    .video-placeholder {
        width: 100%;
        height: 200px;
        background: rgba(0,0,0,0.3);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        margin-bottom: 1rem;
        cursor: pointer;
        transition: background 0.3s;
    }

    .video-placeholder:hover {
        background: rgba(0,0,0,0.5);
    }

    .course-price {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .course-price.free {
        color: #28a745;
    }

    .btn-enroll {
        width: 100%;
        padding: 1rem;
        font-size: 1.2rem;
        font-weight: bold;
        background: var(--secondary-color);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: background 0.3s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-enroll:hover {
        background: #e67e22;
        color: white;
    }

    .btn-enroll.enrolled {
        background: #28a745;
    }

    .course-content {
        padding: 3rem 0;
    }

    .content-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 3rem;
    }

    .content-section {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.5rem;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .lessons-list {
        list-style: none;
        padding: 0;
    }

    .lesson-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        transition: all 0.3s;
    }

    .lesson-item:hover {
        border-color: var(--secondary-color);
        background: rgba(243, 156, 18, 0.05);
    }

    .lesson-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .lesson-icon {
        width: 40px;
        height: 40px;
        background: var(--light-bg);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--secondary-color);
    }

    .lesson-details h4 {
        margin-bottom: 0.25rem;
        color: var(--primary-color);
    }

    .lesson-details p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
    }

    .lesson-duration {
        color: #666;
        font-size: 0.9rem;
    }

    .preview-badge {
        background: #28a745;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .requirements-list,
    .learning-list {
        list-style: none;
        padding: 0;
    }

    .requirements-list li,
    .learning-list li {
        padding: 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .requirements-list i {
        color: var(--accent-color);
    }

    .learning-list i {
        color: #28a745;
    }

    .instructor-card {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        background: var(--light-bg);
        border-radius: 10px;
    }

    .instructor-avatar {
        width: 60px;
        height: 60px;
        background: var(--secondary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .instructor-info h4 {
        margin-bottom: 0.25rem;
        color: var(--primary-color);
    }

    .instructor-info p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
    }

    .related-courses {
        margin-top: 3rem;
    }

    .related-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .related-course {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.3s;
    }

    .related-course:hover {
        transform: translateY(-3px);
    }

    .related-thumbnail {
        width: 100%;
        height: 120px;
        background: linear-gradient(135deg, var(--secondary-color), #e67e22);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
    }

    .related-content {
        padding: 1rem;
    }

    .related-title {
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .related-price {
        color: var(--secondary-color);
        font-weight: bold;
    }

    @media (max-width: 768px) {
        .course-hero-grid,
        .content-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .course-meta {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .course-stats {
            justify-content: space-around;
        }
        
        .related-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<!-- Course Hero -->
<section class="course-hero">
    <div class="container">
        <div class="course-hero-grid">
            <div class="course-info">
                <h1><?php echo e($course->title); ?></h1>
                
                <div class="course-meta">
                    <span><i class="fas fa-tag"></i> <?php echo e($course->category->name); ?></span>
                    <span><i class="fas fa-signal"></i> <?php echo e(ucfirst($course->difficulty_level)); ?></span>
                    <span><i class="fas fa-clock"></i> <?php echo e($course->duration_minutes); ?> minutes</span>
                    <span><i class="fas fa-user"></i> <?php echo e($course->instructor->full_name); ?></span>
                </div>
                
                <p class="course-description"><?php echo e($course->description); ?></p>
                
                <div class="course-stats">
                    <div class="stat-item">
                        <span class="stat-number"><?php echo e($course->enrollment_count); ?></span>
                        <span class="stat-label">Students</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number"><?php echo e($course->lessons_count); ?></span>
                        <span class="stat-label">Lessons</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4.8</span>
                        <span class="stat-label">Rating</span>
                    </div>
                </div>
            </div>
            
            <div class="course-video">
                <div class="video-placeholder">
                    <i class="fas fa-play"></i>
                </div>
                
                <div class="course-price <?php echo e($course->is_free ? 'free' : ''); ?>">
                    <?php if($course->is_free): ?>
                        FREE
                    <?php else: ?>
                        $<?php echo e(number_format($course->price, 0)); ?>

                    <?php endif; ?>
                </div>
                
                <?php if($isEnrolled): ?>
                    <a href="<?php echo e(route('courses.learn', $course)); ?>" class="btn-enroll enrolled">
                        <i class="fas fa-play"></i> Continue Learning
                    </a>
                    <p style="margin-top: 1rem; opacity: 0.8;">
                        Progress: <?php echo e(number_format($progress, 1)); ?>%
                    </p>
                <?php else: ?>
                    <?php if(auth()->guard()->check()): ?>
                        <form method="POST" action="<?php echo e(route('courses.enroll', $course)); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="btn-enroll">
                                <i class="fas fa-plus"></i> Enroll Now
                            </button>
                        </form>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="btn-enroll">
                            <i class="fas fa-sign-in-alt"></i> Login to Enroll
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Course Content -->
<section class="course-content">
    <div class="container">
        <div class="content-grid">
            <div>
                <!-- Course Curriculum -->
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-list"></i>
                        Course Curriculum
                    </h2>
                    
                    <ul class="lessons-list">
                        <?php $__currentLoopData = $course->lessons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lesson): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="lesson-item">
                            <div class="lesson-info">
                                <div class="lesson-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="lesson-details">
                                    <h4><?php echo e($lesson->title); ?></h4>
                                    <p><?php echo e($lesson->description); ?></p>
                                </div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <?php if($lesson->is_preview): ?>
                                    <span class="preview-badge">FREE PREVIEW</span>
                                <?php endif; ?>
                                <span class="lesson-duration"><?php echo e($lesson->formatted_duration); ?></span>
                            </div>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>

                <!-- What You'll Learn -->
                <?php if($course->what_you_learn): ?>
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-graduation-cap"></i>
                        What You'll Learn
                    </h2>
                    
                    <ul class="learning-list">
                        <?php $__currentLoopData = $course->what_you_learn; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li>
                            <i class="fas fa-check"></i>
                            <span><?php echo e($item); ?></span>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
                <?php endif; ?>

                <!-- Requirements -->
                <?php if($course->requirements): ?>
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Requirements
                    </h2>
                    
                    <ul class="requirements-list">
                        <?php $__currentLoopData = $course->requirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li>
                            <i class="fas fa-dot-circle"></i>
                            <span><?php echo e($requirement); ?></span>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
                <?php endif; ?>
            </div>
            
            <div>
                <!-- Instructor -->
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-user-tie"></i>
                        Instructor
                    </h2>
                    
                    <div class="instructor-card">
                        <div class="instructor-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="instructor-info">
                            <h4><?php echo e($course->instructor->full_name); ?></h4>
                            <p><?php echo e($course->instructor->bio ?? 'Experienced entrepreneur and mentor'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Courses -->
        <?php if($relatedCourses->count() > 0): ?>
        <div class="related-courses">
            <h2 class="section-title">
                <i class="fas fa-book"></i>
                Related Courses
            </h2>
            
            <div class="related-grid">
                <?php $__currentLoopData = $relatedCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedCourse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('courses.show', $relatedCourse)); ?>" class="related-course">
                    <div class="related-thumbnail">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="related-content">
                        <div class="related-title"><?php echo e($relatedCourse->title); ?></div>
                        <div class="related-price">
                            <?php if($relatedCourse->is_free): ?>
                                FREE
                            <?php else: ?>
                                $<?php echo e(number_format($relatedCourse->price, 0)); ?>

                            <?php endif; ?>
                        </div>
                    </div>
                </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Work Space\THS\LMS\resources\views/courses/show.blade.php ENDPATH**/ ?>