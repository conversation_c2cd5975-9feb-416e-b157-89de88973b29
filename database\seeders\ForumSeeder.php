<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Forum;
use App\Models\ForumPost;
use App\Models\ForumPostReply;
use App\Models\Category;
use App\Models\User;

class ForumSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get categories
        $cryptoCategory = Category::where('slug', 'cryptocurrency')->first();
        $copywritingCategory = Category::where('slug', 'copywriting')->first();
        $ecommerceCategory = Category::where('slug', 'ecommerce')->first();
        $stocksCategory = Category::where('slug', 'stock-trading')->first();

        // Create forums
        $forums = [
            [
                'name' => 'General Discussion',
                'slug' => 'general-discussion',
                'description' => 'General discussions about entrepreneurship, success mindset, and life.',
                'category_id' => $cryptoCategory?->id ?? 1,
                'sort_order' => 1,
                'is_active' => true,
                'requires_subscription' => false,
            ],
            [
                'name' => 'Cryptocurrency Trading',
                'slug' => 'cryptocurrency-trading',
                'description' => 'Discuss crypto trading strategies, market analysis, and investment opportunities.',
                'category_id' => $cryptoCategory?->id ?? 1,
                'sort_order' => 2,
                'is_active' => true,
                'requires_subscription' => true,
            ],
            [
                'name' => 'Copywriting & Marketing',
                'slug' => 'copywriting-marketing',
                'description' => 'Share copywriting tips, marketing strategies, and successful campaigns.',
                'category_id' => $copywritingCategory?->id ?? 3,
                'sort_order' => 3,
                'is_active' => true,
                'requires_subscription' => true,
            ],
            [
                'name' => 'E-commerce Business',
                'slug' => 'ecommerce-business',
                'description' => 'Discuss e-commerce strategies, product sourcing, and scaling techniques.',
                'category_id' => $ecommerceCategory?->id ?? 4,
                'sort_order' => 4,
                'is_active' => true,
                'requires_subscription' => true,
            ],
            [
                'name' => 'Success Stories',
                'slug' => 'success-stories',
                'description' => 'Share your wins, achievements, and success stories with the community.',
                'category_id' => $cryptoCategory?->id ?? 1,
                'sort_order' => 5,
                'is_active' => true,
                'requires_subscription' => false,
            ],
            [
                'name' => 'Questions & Help',
                'slug' => 'questions-help',
                'description' => 'Ask questions and get help from the community and mentors.',
                'category_id' => $cryptoCategory?->id ?? 1,
                'sort_order' => 6,
                'is_active' => true,
                'requires_subscription' => false,
            ],
        ];

        foreach ($forums as $forumData) {
            $forum = Forum::firstOrCreate(
                ['slug' => $forumData['slug']],
                $forumData
            );

            // Create some sample posts for each forum
            $this->createSamplePosts($forum);
        }
    }

    private function createSamplePosts($forum)
    {
        // Get a mentor user
        $mentor = User::where('email', '<EMAIL>')->first();
        if (!$mentor) {
            return;
        }

        $posts = [];

        switch ($forum->slug) {
            case 'general-discussion':
                $posts = [
                    [
                        'title' => 'Welcome to The Real World Community!',
                        'content' => "Welcome everyone to our exclusive community! This is where winners gather to share knowledge, strategies, and support each other on the path to success.\n\nFeel free to introduce yourself and let us know what you're working on. Remember, we're all here to help each other win!",
                        'is_pinned' => true,
                    ],
                    [
                        'title' => 'Daily Motivation Thread',
                        'content' => "Share your daily wins, no matter how small. Every step forward counts!\n\nWhat did you accomplish today that moved you closer to your goals?",
                        'is_pinned' => false,
                    ],
                ];
                break;

            case 'cryptocurrency-trading':
                $posts = [
                    [
                        'title' => 'Market Analysis: Bitcoin Outlook',
                        'content' => "Here's my analysis of the current Bitcoin market conditions:\n\n1. Technical indicators are showing...\n2. Market sentiment is...\n3. Key levels to watch...\n\nWhat are your thoughts on the current market?",
                        'is_pinned' => false,
                    ],
                ];
                break;

            case 'success-stories':
                $posts = [
                    [
                        'title' => 'From Zero to $10K Monthly - My Journey',
                        'content' => "I wanted to share my success story with the community. 6 months ago, I was broke and desperate. Today, I'm making $10K+ per month.\n\nHere's exactly what I did:\n\n1. Started with copywriting\n2. Built my first client base\n3. Scaled with systems\n\nAMA about my journey!",
                        'is_pinned' => false,
                    ],
                ];
                break;

            default:
                $posts = [
                    [
                        'title' => 'Getting Started in ' . $forum->name,
                        'content' => "This is a sample post to get the discussion started in the {$forum->name} forum.\n\nFeel free to share your thoughts, questions, and experiences here!",
                        'is_pinned' => false,
                    ],
                ];
        }

        foreach ($posts as $postData) {
            $post = ForumPost::create([
                'forum_id' => $forum->id,
                'user_id' => $mentor->id,
                'title' => $postData['title'],
                'content' => $postData['content'],
                'is_pinned' => $postData['is_pinned'],
                'views' => rand(10, 100),
            ]);

            // Add some sample replies
            if (!$post->is_pinned) {
                ForumPostReply::create([
                    'forum_post_id' => $post->id,
                    'user_id' => $mentor->id,
                    'content' => "Great post! Thanks for sharing this valuable information with the community.",
                ]);
            }
        }
    }
}
