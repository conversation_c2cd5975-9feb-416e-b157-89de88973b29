@extends('layouts.app')

@section('title', 'My Courses')

@section('content')
<style>
    .courses-dashboard {
        padding: 2rem 0;
    }

    .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .dashboard-title {
        font-size: 2.5rem;
        color: var(--primary-color);
    }

    .filter-tabs {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .tab-button {
        padding: 0.75rem 1.5rem;
        background: white;
        border: 2px solid #e1e5e9;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none;
        color: #666;
        font-weight: 500;
    }

    .tab-button.active {
        background: var(--secondary-color);
        border-color: var(--secondary-color);
        color: white;
    }

    .tab-button:hover {
        border-color: var(--secondary-color);
        color: var(--secondary-color);
    }

    .tab-button.active:hover {
        color: white;
    }

    .courses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
    }

    .course-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s;
    }

    .course-card:hover {
        transform: translateY(-5px);
    }

    .course-thumbnail {
        width: 100%;
        height: 180px;
        background: linear-gradient(135deg, var(--secondary-color), #e67e22);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        position: relative;
    }

    .course-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .status-active {
        background: #3498db;
        color: white;
    }

    .status-completed {
        background: #28a745;
        color: white;
    }

    .course-content {
        padding: 1.5rem;
    }

    .course-category {
        color: var(--secondary-color);
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .course-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 1rem;
        line-height: 1.3;
    }

    .progress-section {
        margin-bottom: 1.5rem;
    }

    .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .progress-label {
        font-weight: 500;
        color: var(--primary-color);
    }

    .progress-percentage {
        font-weight: bold;
        color: var(--secondary-color);
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e1e5e9;
        border-radius: 4px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--secondary-color), #e67e22);
        transition: width 0.3s;
        border-radius: 4px;
    }

    .course-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
        color: #666;
    }

    .course-actions {
        display: flex;
        gap: 1rem;
    }

    .btn-course {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s;
        text-align: center;
        flex: 1;
    }

    .btn-primary {
        background: var(--secondary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #e67e22;
        color: white;
    }

    .btn-secondary {
        background: transparent;
        color: var(--primary-color);
        border: 2px solid #e1e5e9;
    }

    .btn-secondary:hover {
        border-color: var(--secondary-color);
        color: var(--secondary-color);
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #666;
    }

    .empty-state i {
        font-size: 4rem;
        color: #ccc;
        margin-bottom: 1rem;
    }

    .stats-overview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stat-icon {
        font-size: 2rem;
        color: var(--secondary-color);
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }

    @media (max-width: 768px) {
        .courses-grid {
            grid-template-columns: 1fr;
        }
        
        .dashboard-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
        
        .filter-tabs {
            flex-wrap: wrap;
        }
        
        .course-actions {
            flex-direction: column;
        }
    }
</style>

<div class="container courses-dashboard">
    <!-- Header -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">My Courses</h1>
        <a href="{{ route('courses.index') }}" class="btn-course btn-primary">
            <i class="fas fa-plus"></i> Browse More Courses
        </a>
    </div>

    <!-- Stats Overview -->
    <div class="stats-overview">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-book-open"></i>
            </div>
            <div class="stat-number">{{ $enrollments->total() }}</div>
            <div class="stat-label">Total Enrolled</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="stat-number">{{ $enrollments->where('status', 'completed')->count() }}</div>
            <div class="stat-label">Completed</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-play"></i>
            </div>
            <div class="stat-number">{{ $enrollments->where('status', 'active')->count() }}</div>
            <div class="stat-label">In Progress</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-number">{{ number_format($enrollments->avg('progress_percentage'), 1) }}%</div>
            <div class="stat-label">Avg Progress</div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <a href="{{ route('dashboard.courses') }}" class="tab-button {{ !request('filter') ? 'active' : '' }}">
            All Courses
        </a>
        <a href="{{ route('dashboard.courses') }}?filter=active" class="tab-button {{ request('filter') === 'active' ? 'active' : '' }}">
            In Progress
        </a>
        <a href="{{ route('dashboard.courses') }}?filter=completed" class="tab-button {{ request('filter') === 'completed' ? 'active' : '' }}">
            Completed
        </a>
    </div>

    <!-- Courses Grid -->
    @if($enrollments->count() > 0)
        <div class="courses-grid">
            @foreach($enrollments as $enrollment)
            <div class="course-card">
                <div class="course-thumbnail">
                    <i class="fas fa-play"></i>
                    <div class="course-status {{ $enrollment->status === 'completed' ? 'status-completed' : 'status-active' }}">
                        {{ $enrollment->status_label }}
                    </div>
                </div>
                
                <div class="course-content">
                    <div class="course-category">{{ $enrollment->course->category->name }}</div>
                    <h3 class="course-title">{{ $enrollment->course->title }}</h3>
                    
                    <div class="progress-section">
                        <div class="progress-header">
                            <span class="progress-label">Progress</span>
                            <span class="progress-percentage">{{ number_format($enrollment->progress_percentage, 1) }}%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ $enrollment->progress_percentage }}%"></div>
                        </div>
                    </div>
                    
                    <div class="course-meta">
                        <span><i class="fas fa-clock"></i> {{ $enrollment->course->duration_minutes }}min</span>
                        <span><i class="fas fa-calendar"></i> Enrolled {{ $enrollment->enrolled_at->diffForHumans() }}</span>
                    </div>
                    
                    <div class="course-actions">
                        @if($enrollment->status === 'completed')
                            <a href="{{ route('courses.learn', $enrollment->course) }}" class="btn-course btn-secondary">
                                <i class="fas fa-redo"></i> Review
                            </a>
                        @else
                            <a href="{{ route('courses.learn', $enrollment->course) }}" class="btn-course btn-primary">
                                <i class="fas fa-play"></i> Continue
                            </a>
                        @endif
                        <a href="{{ route('courses.show', $enrollment->course) }}" class="btn-course btn-secondary">
                            <i class="fas fa-info"></i> Details
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div style="display: flex; justify-content: center; margin-top: 3rem;">
            {{ $enrollments->appends(request()->query())->links() }}
        </div>
    @else
        <div class="empty-state">
            <i class="fas fa-book-open"></i>
            <h3>No courses found</h3>
            <p>
                @if(request('filter') === 'completed')
                    You haven't completed any courses yet. Keep learning!
                @elseif(request('filter') === 'active')
                    You don't have any courses in progress.
                @else
                    You haven't enrolled in any courses yet.
                @endif
            </p>
            <a href="{{ route('courses.index') }}" class="btn-course btn-primary">
                Browse Courses
            </a>
        </div>
    @endif
</div>
@endsection
