<?php $__env->startSection('title', 'Live Events'); ?>

<?php $__env->startSection('content'); ?>
<style>
    .events-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
    }

    .events-hero h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .events-hero p {
        font-size: 1.1rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
    }

    .events-container {
        padding: 3rem 0;
    }

    .filters-section {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
    }

    .filter-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }

    .filter-select,
    .filter-input {
        padding: 0.75rem;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        font-size: 1rem;
        transition: border-color 0.3s;
    }

    .filter-select:focus,
    .filter-input:focus {
        outline: none;
        border-color: var(--secondary-color);
    }

    .btn-filter {
        padding: 0.75rem 1.5rem;
        background: var(--secondary-color);
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 500;
        transition: background 0.3s;
    }

    .btn-filter:hover {
        background: #e67e22;
    }

    .events-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .events-count {
        color: #666;
        font-size: 1.1rem;
    }

    .sort-dropdown {
        padding: 0.5rem 1rem;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        background: white;
        cursor: pointer;
    }

    .events-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .event-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s, box-shadow 0.3s;
        position: relative;
    }

    .event-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .event-thumbnail {
        width: 100%;
        height: 200px;
        background: linear-gradient(135deg, var(--secondary-color), #e67e22);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        position: relative;
    }

    .event-status {
        position: absolute;
        top: 1rem;
        left: 1rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
    }

    .badge-live {
        background: #e74c3c;
        color: white;
        animation: pulse 2s infinite;
    }

    .badge-scheduled {
        background: #3498db;
        color: white;
    }

    .badge-completed {
        background: #95a5a6;
        color: white;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .event-date {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 0.5rem;
        border-radius: 8px;
        text-align: center;
        font-size: 0.9rem;
    }

    .event-content {
        padding: 1.5rem;
    }

    .event-category {
        color: var(--secondary-color);
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .event-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.75rem;
        line-height: 1.3;
    }

    .event-description {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .event-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        color: #666;
    }

    .event-meta span {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .event-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .event-attendees {
        font-size: 0.9rem;
        color: #666;
    }

    .btn-event {
        padding: 0.75rem 1.5rem;
        background: var(--secondary-color);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 500;
        transition: background 0.3s;
    }

    .btn-event:hover {
        background: #e67e22;
        color: white;
    }

    .btn-event.disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .no-events {
        text-align: center;
        padding: 4rem 2rem;
        color: #666;
    }

    .no-events i {
        font-size: 4rem;
        color: #ccc;
        margin-bottom: 1rem;
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }

    @media (max-width: 768px) {
        .events-grid {
            grid-template-columns: 1fr;
        }
        
        .filters-grid {
            grid-template-columns: 1fr;
        }
        
        .events-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
    }
</style>

<!-- Hero Section -->
<section class="events-hero">
    <div class="container">
        <h1>Live Events & Masterclasses</h1>
        <p>Join exclusive live sessions with successful entrepreneurs and industry experts. Learn directly from the best and get your questions answered in real-time.</p>
    </div>
</section>

<!-- Events Section -->
<section class="events-container">
    <div class="container">
        <!-- Filters -->
        <div class="filters-section">
            <form method="GET" action="<?php echo e(route('events.index')); ?>">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="filter-label">Search Events</label>
                        <input type="text" name="search" class="filter-input" placeholder="Search by title or description..." value="<?php echo e(request('search')); ?>">
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">Category</label>
                        <select name="category" class="filter-select">
                            <option value="">All Categories</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">Time</label>
                        <select name="time_filter" class="filter-select">
                            <option value="upcoming" <?php echo e(request('time_filter', 'upcoming') == 'upcoming' ? 'selected' : ''); ?>>Upcoming</option>
                            <option value="today" <?php echo e(request('time_filter') == 'today' ? 'selected' : ''); ?>>Today</option>
                            <option value="this_week" <?php echo e(request('time_filter') == 'this_week' ? 'selected' : ''); ?>>This Week</option>
                            <option value="past" <?php echo e(request('time_filter') == 'past' ? 'selected' : ''); ?>>Past Events</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">Status</label>
                        <select name="status" class="filter-select">
                            <option value="">All Status</option>
                            <option value="live" <?php echo e(request('status') == 'live' ? 'selected' : ''); ?>>Live Now</option>
                            <option value="scheduled" <?php echo e(request('status') == 'scheduled' ? 'selected' : ''); ?>>Scheduled</option>
                            <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Completed</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <button type="submit" class="btn-filter">
                            <i class="fas fa-search"></i> Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Events Header -->
        <div class="events-header">
            <div class="events-count">
                <?php echo e($events->total()); ?> event<?php echo e($events->total() !== 1 ? 's' : ''); ?> found
            </div>
            
            <form method="GET" action="<?php echo e(route('events.index')); ?>" style="display: inline;">
                <?php $__currentLoopData = request()->except('sort'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <input type="hidden" name="<?php echo e($key); ?>" value="<?php echo e($value); ?>">
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <select name="sort" class="sort-dropdown" onchange="this.form.submit()">
                    <option value="date_asc" <?php echo e(request('sort', 'date_asc') == 'date_asc' ? 'selected' : ''); ?>>Date: Earliest First</option>
                    <option value="date_desc" <?php echo e(request('sort') == 'date_desc' ? 'selected' : ''); ?>>Date: Latest First</option>
                    <option value="popular" <?php echo e(request('sort') == 'popular' ? 'selected' : ''); ?>>Most Popular</option>
                    <option value="title" <?php echo e(request('sort') == 'title' ? 'selected' : ''); ?>>Title A-Z</option>
                </select>
            </form>
        </div>

        <!-- Events Grid -->
        <?php if($events->count() > 0): ?>
            <div class="events-grid">
                <?php $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="event-card">
                    <div class="event-thumbnail">
                        <i class="fas fa-video"></i>
                        <div class="event-status <?php echo e($event->status_badge_class); ?>">
                            <?php echo e($event->status_label); ?>

                        </div>
                        <div class="event-date">
                            <div><?php echo e($event->scheduled_at->format('M j')); ?></div>
                            <div><?php echo e($event->scheduled_at->format('g:i A')); ?></div>
                        </div>
                    </div>
                    
                    <div class="event-content">
                        <div class="event-category"><?php echo e($event->category->name); ?></div>
                        <h3 class="event-title"><?php echo e($event->title); ?></h3>
                        <p class="event-description"><?php echo e($event->description); ?></p>
                        
                        <div class="event-meta">
                            <span><i class="fas fa-clock"></i> <?php echo e($event->formatted_duration); ?></span>
                            <span><i class="fas fa-user"></i> <?php echo e($event->host->full_name); ?></span>
                            <?php if($event->requires_subscription): ?>
                                <span><i class="fas fa-crown"></i> Premium</span>
                            <?php else: ?>
                                <span><i class="fas fa-unlock"></i> Free</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="event-footer">
                            <div class="event-attendees">
                                <i class="fas fa-users"></i>
                                <?php echo e($event->registration_count); ?><?php echo e($event->max_attendees ? '/' . $event->max_attendees : ''); ?> registered
                            </div>
                            <a href="<?php echo e(route('events.show', $event)); ?>" class="btn-event">
                                <?php if($event->is_live): ?>
                                    <i class="fas fa-play"></i> Join Live
                                <?php elseif($event->is_past && $event->recording_url): ?>
                                    <i class="fas fa-play"></i> Watch Recording
                                <?php else: ?>
                                    <i class="fas fa-info"></i> View Details
                                <?php endif; ?>
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper">
                <?php echo e($events->appends(request()->query())->links()); ?>

            </div>
        <?php else: ?>
            <div class="no-events">
                <i class="fas fa-calendar-times"></i>
                <h3>No events found</h3>
                <p>
                    <?php if(request('time_filter') === 'past'): ?>
                        No past events match your criteria.
                    <?php elseif(request('time_filter') === 'today'): ?>
                        No events scheduled for today.
                    <?php else: ?>
                        No upcoming events match your criteria.
                    <?php endif; ?>
                </p>
                <a href="<?php echo e(route('events.index')); ?>" class="btn-event">View All Events</a>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Work Space\THS\LMS\resources\views/events/index.blade.php ENDPATH**/ ?>