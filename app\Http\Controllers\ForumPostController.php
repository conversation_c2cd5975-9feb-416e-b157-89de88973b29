<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Forum;
use App\Models\ForumPost;
use App\Models\ForumPostReply;
use Illuminate\Support\Facades\Auth;

class ForumPostController extends Controller
{
    /**
     * Display the specified post.
     */
    public function show(Forum $forum, ForumPost $post)
    {
        $post->load(['user', 'forum']);

        // Increment view count
        $post->increment('views');

        // Get replies with pagination
        $replies = $post->replies()
            ->with(['user', 'replies.user'])
            ->whereNull('parent_id') // Only top-level replies
            ->orderBy('created_at', 'asc')
            ->paginate(20);

        return view('forums.post', compact('forum', 'post', 'replies'));
    }

    /**
     * Store a reply to the post.
     */
    public function reply(Request $request, Forum $forum, ForumPost $post)
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to reply.');
        }

        $request->validate([
            'content' => 'required|string|min:5',
            'parent_id' => 'nullable|exists:forum_post_replies,id',
        ]);

        $reply = ForumPostReply::create([
            'forum_post_id' => $post->id,
            'user_id' => Auth::id(),
            'parent_id' => $request->parent_id,
            'content' => $request->content,
        ]);

        // Update post's updated_at timestamp
        $post->touch();

        return redirect()->back()->with('success', 'Reply posted successfully!');
    }

    /**
     * Like/unlike a post.
     */
    public function toggleLike(Request $request, Forum $forum, ForumPost $post)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user = Auth::user();
        $existingLike = $post->likes()->where('user_id', $user->id)->first();

        if ($existingLike) {
            $existingLike->delete();
            $liked = false;
        } else {
            $post->likes()->create(['user_id' => $user->id]);
            $liked = true;
        }

        return response()->json([
            'liked' => $liked,
            'likes_count' => $post->likes()->count()
        ]);
    }

    /**
     * Edit a post.
     */
    public function edit(Forum $forum, ForumPost $post)
    {
        if (!Auth::check() || Auth::id() !== $post->user_id) {
            abort(403, 'Unauthorized');
        }

        return view('forums.edit-post', compact('forum', 'post'));
    }

    /**
     * Update a post.
     */
    public function update(Request $request, Forum $forum, ForumPost $post)
    {
        if (!Auth::check() || Auth::id() !== $post->user_id) {
            abort(403, 'Unauthorized');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string|min:10',
        ]);

        $post->update([
            'title' => $request->title,
            'content' => $request->content,
        ]);

        return redirect()->route('forums.posts.show', [$forum, $post])
            ->with('success', 'Post updated successfully!');
    }

    /**
     * Delete a post.
     */
    public function destroy(Forum $forum, ForumPost $post)
    {
        if (!Auth::check() || (Auth::id() !== $post->user_id && !Auth::user()->hasRole('admin'))) {
            abort(403, 'Unauthorized');
        }

        $post->delete();

        return redirect()->route('forums.show', $forum)
            ->with('success', 'Post deleted successfully!');
    }
}
