<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\Category;
use App\Models\User;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a mentor user if not exists
        $mentor = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'first_name' => 'Andrew',
            'last_name' => 'Tate',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
            'is_mentor' => true,
        ]);
        $mentor->assignRole('mentor');

        // Get categories
        $cryptoCategory = Category::where('slug', 'cryptocurrency')->first();
        $copywritingCategory = Category::where('slug', 'copywriting')->first();
        $ecommerceCategory = Category::where('slug', 'ecommerce')->first();
        $stocksCategory = Category::where('slug', 'stock-trading')->first();

        // Create courses
        $courses = [
            [
                'title' => 'Cryptocurrency Trading Mastery',
                'slug' => 'cryptocurrency-trading-mastery',
                'description' => 'Master the art of cryptocurrency trading with proven strategies that have generated millions in profits. Learn technical analysis, risk management, and advanced trading techniques.',
                'short_description' => 'Learn profitable crypto trading strategies from a successful trader.',
                'category_id' => $cryptoCategory?->id ?? 1,
                'instructor_id' => $mentor->id,
                'difficulty_level' => 'intermediate',
                'duration_minutes' => 480,
                'price' => 0.00,
                'is_free' => true,
                'is_featured' => true,
                'status' => 'published',
                'requirements' => ['Basic understanding of cryptocurrency', 'Computer with internet access'],
                'what_you_learn' => [
                    'Technical analysis fundamentals',
                    'Risk management strategies',
                    'Portfolio diversification',
                    'Market psychology',
                    'Advanced trading techniques'
                ],
                'published_at' => now(),
            ],
            [
                'title' => 'High-Converting Copywriting',
                'slug' => 'high-converting-copywriting',
                'description' => 'Learn the secrets of persuasive copywriting that converts readers into customers. Master sales pages, email sequences, and advertising copy that generates millions in revenue.',
                'short_description' => 'Write copy that sells and converts like crazy.',
                'category_id' => $copywritingCategory?->id ?? 3,
                'instructor_id' => $mentor->id,
                'difficulty_level' => 'beginner',
                'duration_minutes' => 360,
                'price' => 197.00,
                'is_free' => false,
                'is_featured' => true,
                'status' => 'published',
                'requirements' => ['Basic writing skills', 'Willingness to practice'],
                'what_you_learn' => [
                    'Psychology of persuasion',
                    'Sales page structure',
                    'Email marketing sequences',
                    'Ad copy that converts',
                    'A/B testing strategies'
                ],
                'published_at' => now(),
            ],
            [
                'title' => 'E-commerce Empire Building',
                'slug' => 'ecommerce-empire-building',
                'description' => 'Build a profitable e-commerce business from scratch. Learn product research, supplier sourcing, marketing strategies, and scaling techniques used by 7-figure entrepreneurs.',
                'short_description' => 'Build a profitable online store that generates passive income.',
                'category_id' => $ecommerceCategory?->id ?? 4,
                'instructor_id' => $mentor->id,
                'difficulty_level' => 'intermediate',
                'duration_minutes' => 600,
                'price' => 297.00,
                'is_free' => false,
                'is_featured' => true,
                'status' => 'published',
                'requirements' => ['Basic computer skills', 'Starting capital ($500-1000)'],
                'what_you_learn' => [
                    'Product research methods',
                    'Supplier negotiation',
                    'Store optimization',
                    'Facebook advertising',
                    'Scaling strategies'
                ],
                'published_at' => now(),
            ],
        ];

        foreach ($courses as $courseData) {
            $course = Course::firstOrCreate(
                ['slug' => $courseData['slug']],
                $courseData
            );

            // Create lessons for each course
            $this->createLessonsForCourse($course);
        }
    }

    private function createLessonsForCourse($course)
    {
        $lessons = [];

        switch ($course->slug) {
            case 'cryptocurrency-trading-mastery':
                $lessons = [
                    [
                        'title' => 'Introduction to Cryptocurrency Trading',
                        'description' => 'Welcome to the course! Learn what you\'ll achieve and set up your trading environment.',
                        'type' => 'video',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'video_provider' => 'youtube',
                        'duration_minutes' => 15,
                        'is_preview' => true,
                        'sort_order' => 1,
                    ],
                    [
                        'title' => 'Understanding Market Fundamentals',
                        'description' => 'Learn the basics of cryptocurrency markets and how they operate.',
                        'type' => 'video',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'video_provider' => 'youtube',
                        'duration_minutes' => 25,
                        'sort_order' => 2,
                    ],
                    [
                        'title' => 'Technical Analysis Basics',
                        'description' => 'Master chart reading and technical indicators for better trading decisions.',
                        'type' => 'video',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'video_provider' => 'youtube',
                        'duration_minutes' => 35,
                        'sort_order' => 3,
                    ],
                    [
                        'title' => 'Risk Management Strategies',
                        'description' => 'Protect your capital with proven risk management techniques.',
                        'type' => 'video',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'video_provider' => 'youtube',
                        'duration_minutes' => 30,
                        'sort_order' => 4,
                    ],
                ];
                break;

            case 'high-converting-copywriting':
                $lessons = [
                    [
                        'title' => 'The Psychology of Persuasion',
                        'description' => 'Understand what makes people buy and how to trigger those emotions.',
                        'type' => 'video',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'video_provider' => 'youtube',
                        'duration_minutes' => 20,
                        'is_preview' => true,
                        'sort_order' => 1,
                    ],
                    [
                        'title' => 'Writing Headlines That Hook',
                        'description' => 'Craft irresistible headlines that grab attention and compel reading.',
                        'type' => 'video',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'video_provider' => 'youtube',
                        'duration_minutes' => 25,
                        'sort_order' => 2,
                    ],
                    [
                        'title' => 'Sales Page Structure',
                        'description' => 'Learn the proven formula for high-converting sales pages.',
                        'type' => 'video',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'video_provider' => 'youtube',
                        'duration_minutes' => 30,
                        'sort_order' => 3,
                    ],
                ];
                break;

            default:
                $lessons = [
                    [
                        'title' => 'Course Introduction',
                        'description' => 'Welcome to the course and overview of what you\'ll learn.',
                        'type' => 'video',
                        'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        'video_provider' => 'youtube',
                        'duration_minutes' => 10,
                        'is_preview' => true,
                        'sort_order' => 1,
                    ],
                ];
        }

        foreach ($lessons as $lessonData) {
            $lessonData['course_id'] = $course->id;
            $lessonData['slug'] = \Str::slug($lessonData['title']);
            $lessonData['is_published'] = true;

            Lesson::firstOrCreate(
                ['course_id' => $course->id, 'slug' => $lessonData['slug']],
                $lessonData
            );
        }
    }
}
