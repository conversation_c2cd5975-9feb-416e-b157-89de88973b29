@extends('layouts.app')

@section('title', $event->title)

@section('content')
<style>
    .event-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
        color: white;
        padding: 3rem 0;
    }

    .event-hero-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 3rem;
        align-items: center;
    }

    .event-info h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .event-meta {
        display: flex;
        gap: 2rem;
        margin-bottom: 1.5rem;
        font-size: 1rem;
        flex-wrap: wrap;
    }

    .event-meta span {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        opacity: 0.9;
    }

    .event-description {
        font-size: 1.1rem;
        line-height: 1.6;
        opacity: 0.9;
        margin-bottom: 2rem;
    }

    .event-stats {
        display: flex;
        gap: 2rem;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        display: block;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .event-registration {
        background: rgba(255,255,255,0.1);
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
    }

    .event-status {
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        font-size: 1rem;
        font-weight: bold;
        text-transform: uppercase;
        margin-bottom: 1.5rem;
        display: inline-block;
    }

    .status-live {
        background: #e74c3c;
        color: white;
        animation: pulse 2s infinite;
    }

    .status-scheduled {
        background: #3498db;
        color: white;
    }

    .status-completed {
        background: #95a5a6;
        color: white;
    }

    .event-datetime {
        font-size: 1.3rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .btn-register {
        width: 100%;
        padding: 1rem;
        font-size: 1.2rem;
        font-weight: bold;
        background: var(--secondary-color);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: background 0.3s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-register:hover {
        background: #e67e22;
        color: white;
    }

    .btn-register.registered {
        background: #28a745;
    }

    .btn-register.disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .btn-register.live {
        background: #e74c3c;
        animation: pulse 2s infinite;
    }

    .event-content {
        padding: 3rem 0;
    }

    .content-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 3rem;
    }

    .content-section {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.5rem;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .agenda-list {
        list-style: none;
        padding: 0;
    }

    .agenda-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        transition: all 0.3s;
    }

    .agenda-item:hover {
        border-color: var(--secondary-color);
        background: rgba(243, 156, 18, 0.05);
    }

    .agenda-icon {
        width: 40px;
        height: 40px;
        background: var(--light-bg);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--secondary-color);
        margin-right: 1rem;
    }

    .resources-list {
        list-style: none;
        padding: 0;
    }

    .resources-list li {
        padding: 0.75rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        border-bottom: 1px solid #f8f9fa;
    }

    .resources-list li:last-child {
        border-bottom: none;
    }

    .resources-list i {
        color: var(--secondary-color);
        width: 16px;
    }

    .host-card {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        background: var(--light-bg);
        border-radius: 10px;
    }

    .host-avatar {
        width: 60px;
        height: 60px;
        background: var(--secondary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .host-info h4 {
        margin-bottom: 0.25rem;
        color: var(--primary-color);
    }

    .host-info p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
    }

    .attendees-section {
        max-height: 300px;
        overflow-y: auto;
    }

    .attendee-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .attendee-item:last-child {
        border-bottom: none;
    }

    .attendee-avatar {
        width: 35px;
        height: 35px;
        background: var(--secondary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    .attendee-name {
        font-weight: 500;
        color: var(--primary-color);
    }

    .related-events {
        margin-top: 3rem;
    }

    .related-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
    }

    .related-event {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.3s;
    }

    .related-event:hover {
        transform: translateY(-3px);
    }

    .related-thumbnail {
        width: 100%;
        height: 120px;
        background: linear-gradient(135deg, var(--secondary-color), #e67e22);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
    }

    .related-content {
        padding: 1rem;
    }

    .related-title {
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .related-date {
        color: #666;
        font-size: 0.9rem;
    }

    @media (max-width: 768px) {
        .event-hero-grid,
        .content-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .event-meta {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .event-stats {
            justify-content: space-around;
        }
        
        .related-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<!-- Event Hero -->
<section class="event-hero">
    <div class="container">
        <div class="event-hero-grid">
            <div class="event-info">
                <h1>{{ $event->title }}</h1>
                
                <div class="event-meta">
                    <span><i class="fas fa-tag"></i> {{ $event->category->name }}</span>
                    <span><i class="fas fa-clock"></i> {{ $event->formatted_duration }}</span>
                    <span><i class="fas fa-user"></i> {{ $event->host->full_name }}</span>
                    @if($event->requires_subscription)
                        <span><i class="fas fa-crown"></i> Premium Event</span>
                    @else
                        <span><i class="fas fa-unlock"></i> Free Event</span>
                    @endif
                </div>
                
                <p class="event-description">{{ $event->description }}</p>
                
                <div class="event-stats">
                    <div class="stat-item">
                        <span class="stat-number">{{ $event->registration_count }}</span>
                        <span class="stat-label">Registered</span>
                    </div>
                    @if($event->max_attendees)
                    <div class="stat-item">
                        <span class="stat-number">{{ $event->max_attendees }}</span>
                        <span class="stat-label">Max Capacity</span>
                    </div>
                    @endif
                    <div class="stat-item">
                        <span class="stat-number">{{ $event->scheduled_at->format('M j') }}</span>
                        <span class="stat-label">{{ $event->scheduled_at->format('Y') }}</span>
                    </div>
                </div>
            </div>
            
            <div class="event-registration">
                <div class="event-status {{ $event->status_badge_class }}">
                    {{ $event->status_label }}
                </div>
                
                <div class="event-datetime">
                    {{ $event->scheduled_at->format('l, F j, Y') }}<br>
                    {{ $event->scheduled_at->format('g:i A T') }}
                </div>
                
                @if($event->is_live && $isRegistered)
                    <a href="{{ route('events.join', $event) }}" class="btn-register live">
                        <i class="fas fa-video"></i> Join Live Now
                    </a>
                @elseif($event->is_past && $event->recording_url)
                    <a href="{{ $event->recording_url }}" class="btn-register" target="_blank">
                        <i class="fas fa-play"></i> Watch Recording
                    </a>
                @elseif($isRegistered)
                    <form method="POST" action="{{ route('events.unregister', $event) }}">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn-register registered" 
                                onclick="return confirm('Are you sure you want to unregister?')">
                            <i class="fas fa-check"></i> Registered - Click to Cancel
                        </button>
                    </form>
                @elseif($event->is_full)
                    <button class="btn-register disabled" disabled>
                        <i class="fas fa-users"></i> Event Full
                    </button>
                @elseif($event->is_past)
                    <button class="btn-register disabled" disabled>
                        <i class="fas fa-clock"></i> Event Ended
                    </button>
                @else
                    @auth
                        @if($event->canRegister(auth()->user()))
                            <form method="POST" action="{{ route('events.register', $event) }}">
                                @csrf
                                <button type="submit" class="btn-register">
                                    <i class="fas fa-plus"></i> Register Now
                                </button>
                            </form>
                        @else
                            <a href="{{ route('pricing') }}" class="btn-register">
                                <i class="fas fa-crown"></i> Subscribe to Register
                            </a>
                        @endif
                    @else
                        <a href="{{ route('login') }}" class="btn-register">
                            <i class="fas fa-sign-in-alt"></i> Login to Register
                        </a>
                    @endauth
                @endif
                
                @if($isRegistered)
                    <p style="margin-top: 1rem; opacity: 0.8; font-size: 0.9rem;">
                        <i class="fas fa-bell"></i> You'll receive a reminder before the event starts
                    </p>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Event Content -->
<section class="event-content">
    <div class="container">
        <div class="content-grid">
            <div>
                <!-- Event Agenda -->
                @if($event->agenda)
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-list"></i>
                        Event Agenda
                    </h2>
                    
                    <ul class="agenda-list">
                        @foreach($event->agenda as $item)
                        <li class="agenda-item">
                            <div class="agenda-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <span>{{ $item }}</span>
                        </li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <!-- Resources -->
                @if($event->resources)
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-download"></i>
                        Event Resources
                    </h2>
                    
                    <ul class="resources-list">
                        @foreach($event->resources as $resource)
                        <li>
                            <i class="fas fa-file-pdf"></i>
                            <span>{{ $resource }}</span>
                        </li>
                        @endforeach
                    </ul>
                    <p style="color: #666; font-size: 0.9rem; margin-top: 1rem;">
                        <i class="fas fa-info-circle"></i>
                        Resources will be available to registered attendees after the event.
                    </p>
                </div>
                @endif
            </div>
            
            <div>
                <!-- Host Information -->
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-user-tie"></i>
                        Event Host
                    </h2>
                    
                    <div class="host-card">
                        <div class="host-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="host-info">
                            <h4>{{ $event->host->full_name }}</h4>
                            <p>{{ $event->host->bio ?? 'Experienced entrepreneur and mentor' }}</p>
                        </div>
                    </div>
                </div>

                <!-- Registered Attendees -->
                @if($event->registrations->count() > 0)
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-users"></i>
                        Registered Attendees ({{ $event->registration_count }})
                    </h2>
                    
                    <div class="attendees-section">
                        @foreach($event->registrations->take(20) as $registration)
                        <div class="attendee-item">
                            <div class="attendee-avatar">
                                {{ substr($registration->user->first_name, 0, 1) }}{{ substr($registration->user->last_name, 0, 1) }}
                            </div>
                            <div class="attendee-name">{{ $registration->user->full_name }}</div>
                        </div>
                        @endforeach
                        
                        @if($event->registration_count > 20)
                        <p style="text-align: center; color: #666; margin-top: 1rem;">
                            And {{ $event->registration_count - 20 }} more attendees...
                        </p>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Related Events -->
        @if($relatedEvents->count() > 0)
        <div class="related-events">
            <h2 class="section-title">
                <i class="fas fa-calendar"></i>
                Related Events
            </h2>
            
            <div class="related-grid">
                @foreach($relatedEvents as $relatedEvent)
                <a href="{{ route('events.show', $relatedEvent) }}" class="related-event">
                    <div class="related-thumbnail">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="related-content">
                        <div class="related-title">{{ $relatedEvent->title }}</div>
                        <div class="related-date">{{ $relatedEvent->scheduled_at->format('M j, Y g:i A') }}</div>
                    </div>
                </a>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</section>
@endsection
