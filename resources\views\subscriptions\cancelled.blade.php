@extends('layouts.app')

@section('title', 'Payment Cancelled')

@section('content')
<style>
    .cancelled-container {
        max-width: 600px;
        margin: 4rem auto;
        padding: 3rem;
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        text-align: center;
    }

    .cancelled-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        font-size: 3rem;
        color: white;
    }

    .cancelled-title {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .cancelled-message {
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .help-section {
        background: var(--light-bg);
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: left;
    }

    .help-title {
        font-size: 1.3rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
        text-align: center;
    }

    .help-list {
        margin-bottom: 1.5rem;
    }

    .help-list li {
        margin-bottom: 0.75rem;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .help-list i {
        color: var(--secondary-color);
        margin-top: 0.25rem;
        width: 16px;
    }

    .contact-info {
        background: linear-gradient(135deg, var(--primary-color), var(--dark-bg));
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }

    .contact-info h3 {
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }

    .contact-info p {
        margin-bottom: 1rem;
        opacity: 0.9;
    }

    .contact-methods {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .contact-method {
        background: rgba(255,255,255,0.1);
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
    }

    .contact-method i {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        display: block;
    }

    .contact-method strong {
        display: block;
        margin-bottom: 0.25rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-large {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.3s;
    }

    .btn-primary-large {
        background: var(--secondary-color);
        color: white;
        border: none;
    }

    .btn-primary-large:hover {
        background: #e67e22;
        color: white;
    }

    .btn-secondary-large {
        background: transparent;
        color: var(--primary-color);
        border: 2px solid var(--primary-color);
    }

    .btn-secondary-large:hover {
        background: var(--primary-color);
        color: white;
    }

    .special-offer {
        background: linear-gradient(135deg, var(--accent-color), #c0392b);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }

    .special-offer h3 {
        margin-bottom: 1rem;
        font-size: 1.5rem;
    }

    .special-offer p {
        margin-bottom: 1.5rem;
        opacity: 0.9;
    }

    .offer-code {
        background: rgba(255,255,255,0.2);
        padding: 1rem;
        border-radius: 8px;
        font-family: monospace;
        font-size: 1.2rem;
        font-weight: bold;
        text-align: center;
        margin-bottom: 1rem;
        border: 2px dashed rgba(255,255,255,0.5);
    }

    @media (max-width: 768px) {
        .cancelled-container {
            margin: 2rem auto;
            padding: 2rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .contact-methods {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="container">
    <div class="cancelled-container">
        <!-- Cancelled Icon -->
        <div class="cancelled-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>

        <!-- Cancelled Message -->
        <h1 class="cancelled-title">Payment Cancelled</h1>
        <p class="cancelled-message">
            No worries! Your payment was cancelled and no charges were made to your account. 
            We understand that sometimes you need more time to decide.
        </p>

        <!-- Special Offer -->
        <div class="special-offer">
            <h3>🎉 Limited Time Offer!</h3>
            <p>
                Since you showed interest in joining The Real World, we'd like to offer you an exclusive 
                20% discount on your first month. This offer is valid for the next 24 hours only!
            </p>
            <div class="offer-code">
                SAVE20NOW
            </div>
            <p style="font-size: 0.9rem; margin: 0;">
                Use this code at checkout to get 20% off your first month.
            </p>
        </div>

        <!-- Help Section -->
        <div class="help-section">
            <h3 class="help-title">Common Reasons for Payment Issues</h3>
            <ul class="help-list">
                <li>
                    <i class="fas fa-credit-card"></i>
                    <span><strong>Card Issues:</strong> Expired card, insufficient funds, or incorrect details</span>
                </li>
                <li>
                    <i class="fas fa-shield-alt"></i>
                    <span><strong>Security:</strong> Bank security measures blocking international transactions</span>
                </li>
                <li>
                    <i class="fas fa-globe"></i>
                    <span><strong>Location:</strong> Some payment methods may not be available in your region</span>
                </li>
                <li>
                    <i class="fas fa-wifi"></i>
                    <span><strong>Connection:</strong> Poor internet connection during payment processing</span>
                </li>
            </ul>
            <p style="text-align: center; margin: 0; color: #666;">
                If you experienced any of these issues, please try again or contact our support team.
            </p>
        </div>

        <!-- Contact Information -->
        <div class="contact-info">
            <h3>Need Help? We're Here for You!</h3>
            <p>
                Our support team is available 24/7 to help you with any payment issues or questions 
                about our platform. Don't hesitate to reach out!
            </p>
            
            <div class="contact-methods">
                <div class="contact-method">
                    <i class="fas fa-envelope"></i>
                    <strong>Email Support</strong>
                    <span><EMAIL></span>
                </div>
                <div class="contact-method">
                    <i class="fas fa-comments"></i>
                    <strong>Live Chat</strong>
                    <span>Available 24/7</span>
                </div>
                <div class="contact-method">
                    <i class="fas fa-phone"></i>
                    <strong>Phone Support</strong>
                    <span>+****************</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{{ route('pricing') }}" class="btn-large btn-primary-large">
                <i class="fas fa-redo"></i> Try Again
            </a>
            <a href="{{ route('contact') }}" class="btn-large btn-secondary-large">
                <i class="fas fa-headset"></i> Contact Support
            </a>
            <a href="{{ route('home') }}" class="btn-large btn-secondary-large">
                <i class="fas fa-home"></i> Back to Home
            </a>
        </div>

        <!-- Additional Information -->
        <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e1e5e9;">
            <p style="color: #666; font-size: 0.9rem; margin: 0;">
                <i class="fas fa-lock"></i>
                Your payment information is secure and encrypted. We never store your credit card details.
            </p>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Auto-copy discount code when clicked
    document.querySelector('.offer-code').addEventListener('click', function() {
        const code = this.textContent;
        navigator.clipboard.writeText(code).then(function() {
            // Show temporary feedback
            const originalText = document.querySelector('.offer-code').textContent;
            document.querySelector('.offer-code').textContent = 'Copied!';
            setTimeout(() => {
                document.querySelector('.offer-code').textContent = originalText;
            }, 2000);
        });
    });

    // Add click cursor to discount code
    document.querySelector('.offer-code').style.cursor = 'pointer';
    document.querySelector('.offer-code').title = 'Click to copy code';
</script>
@endpush
@endsection
