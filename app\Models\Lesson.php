<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Lesson extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'course_id',
        'title',
        'slug',
        'description',
        'content',
        'type',
        'video_url',
        'video_provider',
        'duration_minutes',
        'resources',
        'is_preview',
        'sort_order',
        'is_published',
    ];

    protected $casts = [
        'resources' => 'array',
        'is_preview' => 'boolean',
        'is_published' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function progress()
    {
        return $this->hasMany(LessonProgress::class);
    }

    public function userProgress($userId = null)
    {
        $userId = $userId ?? auth()->id();
        return $this->hasOne(LessonProgress::class)->where('user_id', $userId);
    }

    /**
     * Scopes
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopePreview($query)
    {
        return $query->where('is_preview', true);
    }

    /**
     * Accessors
     */
    public function getVideoEmbedUrlAttribute()
    {
        if (!$this->video_url) {
            return null;
        }

        switch ($this->video_provider) {
            case 'youtube':
                // Extract video ID from various YouTube URL formats
                preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $this->video_url, $matches);
                return isset($matches[1]) ? "https://www.youtube.com/embed/{$matches[1]}" : null;

            case 'vimeo':
                // Extract video ID from Vimeo URL
                preg_match('/vimeo\.com\/(\d+)/', $this->video_url, $matches);
                return isset($matches[1]) ? "https://player.vimeo.com/video/{$matches[1]}" : null;

            default:
                return $this->video_url;
        }
    }

    public function getFormattedDurationAttribute()
    {
        if (!$this->duration_minutes) {
            return 'N/A';
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm', $hours, $minutes);
        }

        return sprintf('%dm', $minutes);
    }

    public function getResourcesListAttribute()
    {
        return $this->resources ?? [];
    }

    /**
     * Check if user can access this lesson
     */
    public function canAccess($user = null)
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return $this->is_preview;
        }

        // Check if user is enrolled in the course
        $enrollment = $user->enrollments()->where('course_id', $this->course_id)->first();

        return $enrollment || $this->is_preview;
    }
}
