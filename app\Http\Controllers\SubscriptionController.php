<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SubscriptionPlan;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Cashier\Exceptions\IncompletePayment;

class SubscriptionController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show subscription plans.
     */
    public function index()
    {
        $plans = SubscriptionPlan::active()
            ->orderBy('sort_order')
            ->get();

        $user = Auth::user();
        $currentSubscription = $user->subscription('default');

        return view('subscriptions.index', compact('plans', 'currentSubscription'));
    }

    /**
     * Subscribe to a plan.
     */
    public function subscribe(Request $request, SubscriptionPlan $plan)
    {
        $request->validate([
            'payment_method' => 'required|string',
            'billing_cycle' => 'required|in:monthly,quarterly,yearly',
        ]);

        $user = Auth::user();

        // Get the appropriate Stripe price ID based on billing cycle
        $priceId = match($request->billing_cycle) {
            'monthly' => $plan->stripe_monthly_price_id,
            'quarterly' => $plan->stripe_quarterly_price_id,
            'yearly' => $plan->stripe_yearly_price_id,
        };

        if (!$priceId) {
            return redirect()->back()->with('error', 'Selected billing cycle is not available for this plan.');
        }

        try {
            // Create or update the subscription
            if ($user->subscribed('default')) {
                // User already has a subscription, swap to new plan
                $user->subscription('default')->swapAndInvoice($priceId);
                $message = 'Your subscription has been updated successfully!';
            } else {
                // Create new subscription
                $subscription = $user->newSubscription('default', $priceId)
                    ->create($request->payment_method, [
                        'email' => $user->email,
                    ]);
                $message = 'Welcome! Your subscription has been created successfully!';

                // Store subscription details in session for success page
                session([
                    'subscription_plan' => $plan->name,
                    'billing_cycle' => ucfirst($request->billing_cycle),
                    'amount_paid' => match($request->billing_cycle) {
                        'monthly' => $plan->monthly_price,
                        'quarterly' => $plan->quarterly_price,
                        'yearly' => $plan->yearly_price,
                    },
                ]);
            }

            return redirect()->route('payment.success');

        } catch (IncompletePayment $exception) {
            return redirect()->route('cashier.payment', [$exception->payment->id]);
        } catch (\Exception $e) {
            \Log::error('Subscription error: ' . $e->getMessage());
            return redirect()->route('payment.cancelled')->with('error', 'There was an error processing your payment. Please try again.');
        }
    }

    /**
     * Show checkout page for a plan.
     */
    public function checkout(Request $request, SubscriptionPlan $plan)
    {
        $billingCycle = $request->get('period', 'yearly');

        if (!in_array($billingCycle, ['monthly', 'quarterly', 'yearly'])) {
            $billingCycle = 'yearly';
        }

        $price = match($billingCycle) {
            'monthly' => $plan->monthly_price,
            'quarterly' => $plan->quarterly_price,
            'yearly' => $plan->yearly_price,
        };

        return view('subscriptions.checkout', compact('plan', 'billingCycle', 'price'));
    }

    /**
     * Cancel subscription.
     */
    public function cancel(Request $request)
    {
        $user = Auth::user();

        if (!$user->subscribed('default')) {
            return redirect()->back()->with('error', 'You do not have an active subscription.');
        }

        $user->subscription('default')->cancel();

        return redirect()->back()->with('success', 'Your subscription has been cancelled. You will continue to have access until the end of your billing period.');
    }

    /**
     * Resume cancelled subscription.
     */
    public function resume(Request $request)
    {
        $user = Auth::user();

        if (!$user->subscription('default') || !$user->subscription('default')->cancelled()) {
            return redirect()->back()->with('error', 'You do not have a cancelled subscription to resume.');
        }

        $user->subscription('default')->resume();

        return redirect()->back()->with('success', 'Your subscription has been resumed successfully!');
    }

    /**
     * Show billing portal.
     */
    public function billingPortal(Request $request)
    {
        return $request->user()->redirectToBillingPortal(route('dashboard'));
    }

    /**
     * Handle successful payment.
     */
    public function paymentSuccess(Request $request)
    {
        return view('subscriptions.success');
    }

    /**
     * Handle cancelled payment.
     */
    public function paymentCancelled(Request $request)
    {
        return view('subscriptions.cancelled');
    }
}
