@extends('layouts.app')

@section('title', 'Scale from zero to $10k/month')

@section('content')
<style>
    .hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
    }

    .hero h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .hero p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.9;
    }

    .hero .btn {
        font-size: 1.1rem;
        padding: 1rem 2rem;
        margin: 0 0.5rem;
    }

    .section {
        padding: 4rem 0;
    }

    .section-title {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 3rem;
        color: var(--primary-color);
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        text-align: center;
        transition: transform 0.3s;
    }

    .feature-card:hover {
        transform: translateY(-5px);
    }

    .feature-icon {
        font-size: 3rem;
        color: var(--secondary-color);
        margin-bottom: 1rem;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .category-card {
        background: var(--light-bg);
        padding: 1.5rem;
        border-radius: 8px;
        text-align: center;
        transition: all 0.3s;
        border: 2px solid transparent;
    }

    .category-card:hover {
        border-color: var(--secondary-color);
        transform: translateY(-3px);
    }

    .category-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .testimonial-card {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .testimonial-content {
        font-style: italic;
        margin-bottom: 1rem;
    }

    .testimonial-author {
        font-weight: bold;
        color: var(--primary-color);
    }

    .cta-section {
        background: var(--secondary-color);
        color: white;
        text-align: center;
        padding: 4rem 0;
    }

    .cta-section h2 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .cta-section p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
    }
</style>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <h1>Scale from zero to $10k/month</h1>
        <p>Join thousands of students learning proven strategies in crypto, copywriting, e-commerce, and more.</p>
        <a href="{{ route('register') }}" class="btn btn-primary">Join The Real World</a>
        <a href="{{ route('pricing') }}" class="btn btn-secondary">View Pricing</a>
    </div>
</section>

<!-- Features Section -->
<section class="section">
    <div class="container">
        <h2 class="section-title">Why Choose The Real World?</h2>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <h3>Expert Mentorship</h3>
                <p>Learn from successful entrepreneurs who have built million-dollar businesses from scratch.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-video"></i>
                </div>
                <h3>Live Training</h3>
                <p>Join live calls and get your questions answered in real-time by industry experts.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>Community</h3>
                <p>Connect with like-minded individuals and build valuable relationships that last a lifetime.</p>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
@if($categories->count() > 0)
<section class="section" style="background: var(--light-bg);">
    <div class="container">
        <h2 class="section-title">Master Multiple Skills</h2>
        <div class="categories-grid">
            @foreach($categories as $category)
            <div class="category-card">
                <div class="category-icon" style="color: {{ $category->color }};">
                    <i class="fas fa-{{ $category->icon }}"></i>
                </div>
                <h3>{{ $category->name }}</h3>
                <p>{{ $category->description }}</p>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Testimonials Section -->
@if($testimonials->count() > 0)
<section class="section">
    <div class="container">
        <h2 class="section-title">Success Stories</h2>
        <div class="testimonials-grid">
            @foreach($testimonials as $testimonial)
            <div class="testimonial-card">
                <div class="testimonial-content">
                    "{{ $testimonial->content }}"
                </div>
                <div class="testimonial-author">
                    - {{ $testimonial->name }}
                    @if($testimonial->title)
                        <br><small>{{ $testimonial->title }}</small>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <h2>Ready to Transform Your Life?</h2>
        <p>Join thousands of students who are already building their path to financial freedom.</p>
        <a href="{{ route('register') }}" class="btn btn-primary" style="background: white; color: var(--secondary-color);">
            Start Your Journey Today
        </a>
    </div>
</section>
@endsection
