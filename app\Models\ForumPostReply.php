<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ForumPostReply extends Model
{
    use HasFactory;

    protected $fillable = [
        'forum_post_id',
        'user_id',
        'parent_id',
        'content',
    ];

    /**
     * Relationships
     */
    public function forumPost()
    {
        return $this->belongsTo(ForumPost::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function parent()
    {
        return $this->belongsTo(ForumPostReply::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(ForumPostReply::class, 'parent_id');
    }

    public function likes()
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    /**
     * Accessors
     */
    public function getFormattedContentAttribute()
    {
        return nl2br(e($this->content));
    }

    public function getLikesCountAttribute()
    {
        return $this->likes()->count();
    }

    /**
     * Check if user has liked this reply
     */
    public function isLikedBy($user = null)
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return false;
        }

        return $this->likes()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if user can edit this reply
     */
    public function canEdit($user = null)
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return false;
        }

        return $user->id === $this->user_id || $user->hasRole('admin');
    }
}
