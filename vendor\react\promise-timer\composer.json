{"name": "react/promise-timer", "description": "A trivial implementation of timeouts for Promises, built on top of ReactPHP.", "keywords": ["Promise", "timeout", "timer", "event-loop", "ReactPHP", "async"], "homepage": "https://github.com/reactphp/promise-timer", "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://clue.engineering/", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "homepage": "https://wyrihaximus.net/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://sorgalla.com/", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://cboden.dev/", "email": "<EMAIL>"}], "require": {"php": ">=5.3", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.7.0 || ^1.2.1"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "autoload": {"psr-4": {"React\\Promise\\Timer\\": "src/"}, "files": ["src/functions_include.php"]}, "autoload-dev": {"psr-4": {"React\\Tests\\Promise\\Timer\\": "tests/"}}}