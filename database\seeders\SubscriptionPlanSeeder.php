<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SubscriptionPlan;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Cadet',
                'slug' => 'cadet',
                'description' => 'Perfect for beginners starting their journey to financial freedom.',
                'monthly_price' => 49.00,
                'quarterly_price' => 129.00,
                'yearly_price' => 449.00,
                'features' => [
                    'Access to basic courses',
                    'Community forum access',
                    'Monthly live Q&A sessions',
                    'Basic support'
                ],
                'max_courses' => 5,
                'live_calls_access' => true,
                'community_access' => true,
                'mentor_access' => false,
                'sort_order' => 1,
                'is_active' => true,
                'is_popular' => false,
            ],
            [
                'name' => 'Challenger',
                'slug' => 'challenger',
                'description' => 'For those ready to take their skills to the next level.',
                'monthly_price' => 99.00,
                'quarterly_price' => 249.00,
                'yearly_price' => 849.00,
                'features' => [
                    'Access to all courses',
                    'Community forum access',
                    'Weekly live sessions',
                    'Priority support',
                    'Exclusive resources'
                ],
                'max_courses' => null, // unlimited
                'live_calls_access' => true,
                'community_access' => true,
                'mentor_access' => false,
                'sort_order' => 2,
                'is_active' => true,
                'is_popular' => true,
            ],
            [
                'name' => 'Hero',
                'slug' => 'hero',
                'description' => 'Advanced training for serious entrepreneurs.',
                'monthly_price' => 199.00,
                'quarterly_price' => 499.00,
                'yearly_price' => 1699.00,
                'features' => [
                    'Everything in Challenger',
                    'Direct mentor access',
                    'Private mastermind group',
                    '1-on-1 monthly calls',
                    'Advanced strategies',
                    'VIP support'
                ],
                'max_courses' => null, // unlimited
                'live_calls_access' => true,
                'community_access' => true,
                'mentor_access' => true,
                'sort_order' => 3,
                'is_active' => true,
                'is_popular' => false,
            ],
            [
                'name' => 'Champion',
                'slug' => 'champion',
                'description' => 'Elite tier for those who want everything.',
                'monthly_price' => 499.00,
                'quarterly_price' => 1299.00,
                'yearly_price' => 4499.00,
                'features' => [
                    'Everything in Hero',
                    'Personal business coaching',
                    'Done-for-you templates',
                    'Weekly 1-on-1 calls',
                    'Direct phone access',
                    'Exclusive events',
                    'White-glove service'
                ],
                'max_courses' => null, // unlimited
                'live_calls_access' => true,
                'community_access' => true,
                'mentor_access' => true,
                'sort_order' => 4,
                'is_active' => true,
                'is_popular' => false,
            ],
        ];

        foreach ($plans as $planData) {
            SubscriptionPlan::firstOrCreate(
                ['slug' => $planData['slug']],
                $planData
            );
        }
    }
}
