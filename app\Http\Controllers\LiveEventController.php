<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\LiveEvent;
use App\Models\EventRegistration;
use App\Models\Category;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class LiveEventController extends Controller
{
    /**
     * Display a listing of live events.
     */
    public function index(Request $request)
    {
        $query = LiveEvent::with(['host', 'category']);

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->where('category_id', $request->category);
        }

        // Filter by status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Filter by time
        if ($request->has('time_filter')) {
            switch ($request->time_filter) {
                case 'upcoming':
                    $query->where('scheduled_at', '>', now());
                    break;
                case 'today':
                    $query->whereDate('scheduled_at', today());
                    break;
                case 'this_week':
                    $query->whereBetween('scheduled_at', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'past':
                    $query->where('scheduled_at', '<', now());
                    break;
            }
        } else {
            // Default to upcoming events
            $query->where('scheduled_at', '>', now());
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sort
        $sortBy = $request->get('sort', 'date_asc');
        switch ($sortBy) {
            case 'date_desc':
                $query->orderBy('scheduled_at', 'desc');
                break;
            case 'popular':
                $query->withCount('registrations')->orderBy('registrations_count', 'desc');
                break;
            case 'title':
                $query->orderBy('title', 'asc');
                break;
            default:
                $query->orderBy('scheduled_at', 'asc');
        }

        $events = $query->paginate(12);
        $categories = Category::active()->orderBy('sort_order')->get();

        return view('events.index', compact('events', 'categories'));
    }

    /**
     * Display the specified event.
     */
    public function show(LiveEvent $event)
    {
        $event->load(['host', 'category', 'registrations.user']);

        $isRegistered = false;
        $registration = null;

        if (Auth::check()) {
            $registration = Auth::user()->eventRegistrations()
                ->where('live_event_id', $event->id)
                ->first();
            $isRegistered = (bool) $registration;
        }

        // Get related events
        $relatedEvents = LiveEvent::where('category_id', $event->category_id)
            ->where('id', '!=', $event->id)
            ->where('scheduled_at', '>', now())
            ->limit(3)
            ->get();

        return view('events.show', compact('event', 'isRegistered', 'registration', 'relatedEvents'));
    }

    /**
     * Register for an event.
     */
    public function register(Request $request, LiveEvent $event)
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to register for events.');
        }

        $user = Auth::user();

        // Check if user is already registered
        if ($user->eventRegistrations()->where('live_event_id', $event->id)->exists()) {
            return redirect()->back()->with('info', 'You are already registered for this event.');
        }

        // Check if event is full
        if ($event->max_attendees && $event->registrations()->count() >= $event->max_attendees) {
            return redirect()->back()->with('error', 'This event is full.');
        }

        // Check if event requires subscription
        if ($event->requires_subscription && !$user->subscribed('default')) {
            return redirect()->route('pricing')->with('error', 'Please subscribe to register for premium events.');
        }

        // Create registration
        EventRegistration::create([
            'user_id' => $user->id,
            'live_event_id' => $event->id,
            'registered_at' => now(),
        ]);

        return redirect()->back()->with('success', 'Successfully registered! You will receive a reminder before the event.');
    }

    /**
     * Unregister from an event.
     */
    public function unregister(Request $request, LiveEvent $event)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $registration = $user->eventRegistrations()->where('live_event_id', $event->id)->first();

        if (!$registration) {
            return redirect()->back()->with('error', 'You are not registered for this event.');
        }

        $registration->delete();

        return redirect()->back()->with('success', 'Successfully unregistered from the event.');
    }

    /**
     * Join live event.
     */
    public function join(LiveEvent $event)
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to join events.');
        }

        $user = Auth::user();

        // Check if user is registered
        $registration = $user->eventRegistrations()->where('live_event_id', $event->id)->first();
        if (!$registration) {
            return redirect()->route('events.show', $event)->with('error', 'You must register for this event first.');
        }

        // Check if event is live or about to start (within 15 minutes)
        $eventTime = Carbon::parse($event->scheduled_at);
        $now = now();

        if ($now->lt($eventTime->subMinutes(15))) {
            return redirect()->route('events.show', $event)
                ->with('error', 'Event has not started yet. You can join 15 minutes before the scheduled time.');
        }

        // Update registration status
        $registration->update(['attended' => true]);

        // Redirect to the live stream URL or show embedded player
        if ($event->stream_url) {
            return redirect($event->stream_url);
        }

        return view('events.live', compact('event', 'registration'));
    }
}
