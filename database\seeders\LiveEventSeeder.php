<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\LiveEvent;
use App\Models\Category;
use App\Models\User;
use Carbon\Carbon;

class LiveEventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get a mentor user
        $mentor = User::where('email', '<EMAIL>')->first();
        if (!$mentor) {
            $mentor = User::create([
                'first_name' => 'Andrew',
                'last_name' => 'Tate',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'is_mentor' => true,
            ]);
            $mentor->assignRole('mentor');
        }

        // Get categories
        $cryptoCategory = Category::where('slug', 'cryptocurrency')->first();
        $copywritingCategory = Category::where('slug', 'copywriting')->first();
        $ecommerceCategory = Category::where('slug', 'ecommerce')->first();

        // Create upcoming events
        $events = [
            [
                'title' => 'Live Crypto Trading Session',
                'slug' => 'live-crypto-trading-session',
                'description' => 'Join Andrew Tate for a live cryptocurrency trading session where he\'ll analyze the markets, share his strategies, and answer your questions in real-time.',
                'category_id' => $cryptoCategory?->id ?? 1,
                'host_id' => $mentor->id,
                'scheduled_at' => now()->addDays(3)->setTime(19, 0), // 7 PM in 3 days
                'duration_minutes' => 90,
                'timezone' => 'UTC',
                'max_attendees' => 500,
                'requires_subscription' => true,
                'status' => 'scheduled',
                'agenda' => [
                    'Market Analysis (15 min)',
                    'Live Trading Demo (30 min)',
                    'Q&A Session (30 min)',
                    'Closing Thoughts (15 min)'
                ],
                'resources' => [
                    'Trading checklist PDF',
                    'Market analysis template',
                    'Recommended exchanges list'
                ],
            ],
            [
                'title' => 'Copywriting Masterclass: Million Dollar Sales Pages',
                'slug' => 'copywriting-masterclass-million-dollar-sales-pages',
                'description' => 'Learn the secrets behind sales pages that have generated millions in revenue. We\'ll break down real examples and teach you the psychology of persuasion.',
                'category_id' => $copywritingCategory?->id ?? 3,
                'host_id' => $mentor->id,
                'scheduled_at' => now()->addDays(7)->setTime(20, 0), // 8 PM in 7 days
                'duration_minutes' => 120,
                'timezone' => 'UTC',
                'max_attendees' => 300,
                'requires_subscription' => true,
                'status' => 'scheduled',
                'agenda' => [
                    'Psychology of Persuasion (20 min)',
                    'Sales Page Breakdown (40 min)',
                    'Live Writing Exercise (30 min)',
                    'Q&A and Feedback (30 min)'
                ],
                'resources' => [
                    'Sales page templates',
                    'Copywriting checklist',
                    'Psychology triggers guide'
                ],
            ],
            [
                'title' => 'E-commerce Empire: Scaling to 7 Figures',
                'slug' => 'ecommerce-empire-scaling-to-7-figures',
                'description' => 'Discover the strategies used to scale e-commerce businesses from startup to 7-figure revenue. Real case studies and actionable insights.',
                'category_id' => $ecommerceCategory?->id ?? 4,
                'host_id' => $mentor->id,
                'scheduled_at' => now()->addDays(10)->setTime(18, 0), // 6 PM in 10 days
                'duration_minutes' => 105,
                'timezone' => 'UTC',
                'max_attendees' => 400,
                'requires_subscription' => true,
                'status' => 'scheduled',
                'agenda' => [
                    'Scaling Fundamentals (25 min)',
                    'Case Study Analysis (30 min)',
                    'Marketing Strategies (30 min)',
                    'Q&A Session (20 min)'
                ],
                'resources' => [
                    'Scaling checklist',
                    'Marketing templates',
                    'Supplier contact list'
                ],
            ],
            [
                'title' => 'Free Mindset Mastery Workshop',
                'slug' => 'free-mindset-mastery-workshop',
                'description' => 'A free introductory workshop on developing the mindset of successful entrepreneurs. Open to all members.',
                'category_id' => $cryptoCategory?->id ?? 1,
                'host_id' => $mentor->id,
                'scheduled_at' => now()->addDays(5)->setTime(17, 0), // 5 PM in 5 days
                'duration_minutes' => 60,
                'timezone' => 'UTC',
                'max_attendees' => 1000,
                'requires_subscription' => false,
                'status' => 'scheduled',
                'agenda' => [
                    'Introduction to Success Mindset (15 min)',
                    'Overcoming Limiting Beliefs (20 min)',
                    'Action Planning (15 min)',
                    'Q&A (10 min)'
                ],
                'resources' => [
                    'Mindset assessment PDF',
                    'Goal setting template'
                ],
            ],
            [
                'title' => 'Past Event: Crypto Market Analysis',
                'slug' => 'past-event-crypto-market-analysis',
                'description' => 'A comprehensive analysis of the cryptocurrency market trends and predictions.',
                'category_id' => $cryptoCategory?->id ?? 1,
                'host_id' => $mentor->id,
                'scheduled_at' => now()->subDays(7)->setTime(19, 0), // 7 days ago
                'duration_minutes' => 90,
                'timezone' => 'UTC',
                'max_attendees' => 500,
                'requires_subscription' => true,
                'status' => 'completed',
                'recording_url' => 'https://example.com/recording/crypto-analysis',
                'agenda' => [
                    'Market Overview (20 min)',
                    'Technical Analysis (30 min)',
                    'Future Predictions (25 min)',
                    'Q&A (15 min)'
                ],
                'resources' => [
                    'Market analysis report',
                    'Trading signals guide'
                ],
            ],
        ];

        foreach ($events as $eventData) {
            LiveEvent::firstOrCreate(
                ['slug' => $eventData['slug']],
                $eventData
            );
        }
    }
}
