# Changelog

All notable changes to `image` will be documented in this file

## 3.8.4 - 2025-06-04

### What's Changed

* Bump dependabot/fetch-metadata from 2.3.0 to 2.4.0 by @dependabot in https://github.com/spatie/image/pull/297
* Update GdDriver.php by @GhostvOne in https://github.com/spatie/image/pull/298

### New Contributors

* @GhostvOne made their first contribution in https://github.com/spatie/image/pull/298

**Full Changelog**: https://github.com/spatie/image/compare/3.8.3...3.8.4

## 3.8.3 - 2025-04-25

### What's Changed

* refactor: simplify loadFile by replacing fopen/fread with file_get_contents by @Ayo<PERSON>-<PERSON>brouk in https://github.com/spatie/image/pull/296
* refactor(image): simplify and merge exif and fileinfo extension checks by @<PERSON>yo<PERSON>-<PERSON><PERSON><PERSON> in https://github.com/spatie/image/pull/295

**Full Changelog**: https://github.com/spatie/image/compare/3.8.2...3.8.3

## 3.8.2 - 2025-04-24

### What's Changed

* refactor: simplify resizeWidth method by @Ayoub-Mabrouk in https://github.com/spatie/image/pull/292
* refactor: simplify and optimize resizeHeight method by @Ayoub-Mabrouk in https://github.com/spatie/image/pull/293
* refactor: simplify resize method by @Ayoub-Mabrouk in https://github.com/spatie/image/pull/294

### New Contributors

* @Ayoub-Mabrouk made their first contribution in https://github.com/spatie/image/pull/292

**Full Changelog**: https://github.com/spatie/image/compare/3.8.1...3.8.2

## 3.8.1 - 2025-03-27

### What's Changed

* Bump dependabot/fetch-metadata from 2.2.0 to 2.3.0 by @dependabot in https://github.com/spatie/image/pull/287
* Bump aglipanci/laravel-pint-action from 2.4 to 2.5 by @dependabot in https://github.com/spatie/image/pull/288
* Allow custom image drivers by @Peeterush in https://github.com/spatie/image/pull/290

**Full Changelog**: https://github.com/spatie/image/compare/3.8.0...3.8.1

## 3.8.0 - 2025-01-17

### What's Changed

* Add -90 rotation degree by @hbakouane in https://github.com/spatie/image/pull/285

### New Contributors

* @hbakouane made their first contribution in https://github.com/spatie/image/pull/285

**Full Changelog**: https://github.com/spatie/image/compare/3.7.5...3.8.0

## 3.7.5 - 2025-01-13

### What's Changed

* PHP 8.4 tests by @erikn69 in https://github.com/spatie/image/pull/277
* Correct documentation for FillMax in resizing-images.md by @ElGovanni in https://github.com/spatie/image/pull/278
* Correction: Update basic-usage.md Selecting a driver" section by @PrabalPradhan1991 in https://github.com/spatie/image/pull/280
* Update basic-usage.md Reverting back the incorrect changes that I suggested by @PrabalPradhan1991 in https://github.com/spatie/image/pull/281
* Do not remove color profiles when resizing canvas. by @Peeterush in https://github.com/spatie/image/pull/284

### New Contributors

* @ElGovanni made their first contribution in https://github.com/spatie/image/pull/278
* @Peeterush made their first contribution in https://github.com/spatie/image/pull/284

**Full Changelog**: https://github.com/spatie/image/compare/3.7.4...3.7.5

## 3.7.4 - 2024-10-07

### What's Changed

* Fix broken link by @Synchro in https://github.com/spatie/image/pull/272
* Update resizing-images.md by @makakken in https://github.com/spatie/image/pull/273
* Improve DX by specifying namespace for GD functions by @alies-dev in https://github.com/spatie/image/pull/274
* Fix issue with palette images when saving a webp file using GD.

### New Contributors

* @makakken made their first contribution in https://github.com/spatie/image/pull/273
* @alies-dev made their first contribution in https://github.com/spatie/image/pull/274

**Full Changelog**: https://github.com/spatie/image/compare/3.7.3...3.7.4

## 3.7.3 - 2024-08-06

### What's Changed

* Update Image.php to fix $prefixWithFormat by @tanshiqi in https://github.com/spatie/image/pull/269

### New Contributors

* @tanshiqi made their first contribution in https://github.com/spatie/image/pull/269

**Full Changelog**: https://github.com/spatie/image/compare/3.7.2...3.7.3

## 3.7.2 - 2024-07-26

### What's Changed

* fix: Apply image format when saving file in ImagickDriver by @nicoverbruggen in https://github.com/spatie/image/pull/268

### New Contributors

* @nicoverbruggen made their first contribution in https://github.com/spatie/image/pull/268

**Full Changelog**: https://github.com/spatie/image/compare/3.7.1...3.7.2

## 3.7.1 - 2024-07-18

### What's Changed

* fix: GdDriver resizeCanvas save alpha channel; by @olexoliinyk0 in https://github.com/spatie/image/pull/266

### New Contributors

* @olexoliinyk0 made their first contribution in https://github.com/spatie/image/pull/266

**Full Changelog**: https://github.com/spatie/image/compare/3.7.0...3.7.1

## 3.7.0 - 2024-07-18

### What's Changed

* Bump dependabot/fetch-metadata from 2.1.0 to 2.2.0 by @dependabot in https://github.com/spatie/image/pull/263
* Fix bug in ImagickDriver::pixelate, Update tests by @psion-ar in https://github.com/spatie/image/pull/267

### New Contributors

* @psion-ar made their first contribution in https://github.com/spatie/image/pull/267

**Full Changelog**: https://github.com/spatie/image/compare/3.6.4...3.7.0

## 3.6.4 - 2024-06-03

### What's Changed

* Implement Fit::FillMax by @timvandijck in https://github.com/spatie/image/pull/258

**Full Changelog**: https://github.com/spatie/image/compare/3.6.3...3.6.4

## 3.6.3 - 2024-05-24

**Full Changelog**: https://github.com/spatie/image/compare/3.6.2...3.6.3

## 3.6.2 - 2024-05-23

### What's Changed

* Update overview.md by @schmeits in https://github.com/spatie/image/pull/253
* Update saving-images.md by @schmeits in https://github.com/spatie/image/pull/256
* Update introduction.md by @schmeits in https://github.com/spatie/image/pull/255
* Update basic-usage.md by @schmeits in https://github.com/spatie/image/pull/254
* Update composer.json by @thanosalexandris in https://github.com/spatie/image/pull/257

### New Contributors

* @schmeits made their first contribution in https://github.com/spatie/image/pull/253
* @thanosalexandris made their first contribution in https://github.com/spatie/image/pull/257

**Full Changelog**: https://github.com/spatie/image/compare/3.6.1...3.6.2

## 3.6.1 - 2024-05-17

### What's Changed

* Ability to resize canvas when keeping fill background a certain color by @OzanKurt in https://github.com/spatie/image/pull/249

### New Contributors

* @OzanKurt made their first contribution in https://github.com/spatie/image/pull/249

**Full Changelog**: https://github.com/spatie/image/compare/3.6.0...3.6.1

## 3.6.0 - 2024-05-07

### What's Changed

* Bump dependabot/fetch-metadata from 1.6.0 to 2.0.0 by @dependabot in https://github.com/spatie/image/pull/244
* Bump dependabot/fetch-metadata from 2.0.0 to 2.1.0 by @dependabot in https://github.com/spatie/image/pull/251
* Convert Unit and Orientation to BackedEnum by @MrMeshok in https://github.com/spatie/image/pull/252

### New Contributors

* @MrMeshok made their first contribution in https://github.com/spatie/image/pull/252

**Full Changelog**: https://github.com/spatie/image/compare/3.5.0...3.6.0

## 3.5.0 - 2024-04-17

### What's Changed

* Fix saving jfif image by @clementbirkle in https://github.com/spatie/image/pull/248

### New Contributors

* @clementbirkle made their first contribution in https://github.com/spatie/image/pull/248

**Full Changelog**: https://github.com/spatie/image/compare/3.4.2...3.5.0

## 3.4.2 - 2024-04-15

### What's Changed

* Bump aglipanci/laravel-pint-action from 2.3.1 to 2.4 by @dependabot in https://github.com/spatie/image/pull/246
* Suppress warnings from exif_read_data by @glennjacobs in https://github.com/spatie/image/pull/247
* Suppress warnings from imagecreatefromstring by @mattmcdonald-uk in https://github.com/spatie/image/pull/243

### New Contributors

* @glennjacobs made their first contribution in https://github.com/spatie/image/pull/247
* @mattmcdonald-uk made their first contribution in https://github.com/spatie/image/pull/243

**Full Changelog**: https://github.com/spatie/image/compare/3.4.0...3.4.2

## 3.4.1 - 2024-04-15

### What's Changed

* Bump aglipanci/laravel-pint-action from 2.3.1 to 2.4 by @dependabot in https://github.com/spatie/image/pull/246
* Suppress warnings from exif_read_data by @glennjacobs in https://github.com/spatie/image/pull/247

### New Contributors

* @glennjacobs made their first contribution in https://github.com/spatie/image/pull/247

**Full Changelog**: https://github.com/spatie/image/compare/3.4.0...3.4.1

## 3.4.0 - 2024-03-05

### What's Changed

* Bump ramsey/composer-install from 2 to 3 by @dependabot in https://github.com/spatie/image/pull/241
* Add a ->text method to the package by @riasvdv in https://github.com/spatie/image/pull/242

### New Contributors

* @riasvdv made their first contribution in https://github.com/spatie/image/pull/242

**Full Changelog**: https://github.com/spatie/image/compare/3.3.8...3.4.0

## 3.3.8 - 2024-03-04

### What's Changed

* Allow lossless saving of Webp with the GD driver by @fabio-ivona in https://github.com/spatie/image/pull/240

### New Contributors

* @fabio-ivona made their first contribution in https://github.com/spatie/image/pull/240

**Full Changelog**: https://github.com/spatie/image/compare/3.3.7...3.3.8

## 3.3.7 - 2024-03-01

### What's Changed

* Fix for Fit::Max not behaving as described in the documentation by @RaBic in https://github.com/spatie/image/pull/239

### New Contributors

* @RaBic made their first contribution in https://github.com/spatie/image/pull/239

**Full Changelog**: https://github.com/spatie/image/compare/3.3.6...3.3.7

## 3.3.6 - 2024-02-26

### What's Changed

* Bugfix for cases where setting a quality did nothing https://github.com/spatie/image/pull/237
* Bugfix for transparent PNG's not staying transparent when working with GD.
* Autorotate images based on their Exif data https://github.com/spatie/image/pull/238

**Full Changelog**: https://github.com/spatie/image/compare/3.3.5...3.3.6

## 3.3.5 - 2024-02-16

### What's Changed

* Update adding-a-watermark.md by @yoeriboven in https://github.com/spatie/image/pull/235
* Manipulating animated GIF's is now supported using Imagick. by @jhorie in https://github.com/spatie/image/pull/234

### New Contributors

* @yoeriboven made their first contribution in https://github.com/spatie/image/pull/235
* @jhorie made their first contribution in https://github.com/spatie/image/pull/234

**Full Changelog**: https://github.com/spatie/image/compare/3.3.4...3.3.5

## 3.3.4 - 2024-01-15

### What's Changed

* Crop issue fixes by @timvandijck in https://github.com/spatie/image/pull/230

**Full Changelog**: https://github.com/spatie/image/compare/3.3.3...3.3.4

## 3.3.3 - 2024-01-05

### What's Changed

* Fix broken fit/crop operation.
* Bump aglipanci/laravel-pint-action from 2.3.0 to 2.3.1 by @dependabot in https://github.com/spatie/image/pull/224
* Fix enum case by @lukasleitsch in https://github.com/spatie/image/pull/226
* update load image with gd driver example by @lukasleitsch in https://github.com/spatie/image/pull/227

### New Contributors

* @lukasleitsch made their first contribution in https://github.com/spatie/image/pull/226

**Full Changelog**: https://github.com/spatie/image/compare/3.3.2...3.3.3

## 3.3.2 - 2023-12-24

### What's Changed

* move ext requirement to dev by @ariaieboy in https://github.com/spatie/image/pull/222

**Full Changelog**: https://github.com/spatie/image/compare/3.3.1...3.3.2

## 3.3.1 - 2023-12-22

### What's Changed

* update GD driver by @ariaieboy in https://github.com/spatie/image/pull/221

**Full Changelog**: https://github.com/spatie/image/compare/3.3.0...3.3.1

## 3.3.0 - 2023-12-21

- add watermark method

## 3.2.0 - 2023-12-18

### What's Changed

* Bump actions/checkout from 3 to 4 by @dependabot in https://github.com/spatie/image/pull/210
* Add `OptimizerChain::setTimeout()` to the image optimization tests and docs by @golubev in https://github.com/spatie/image/pull/208
* Image load fix by @kbond in https://github.com/spatie/image/pull/214

### New Contributors

* @golubev made their first contribution in https://github.com/spatie/image/pull/208
* @kbond made their first contribution in https://github.com/spatie/image/pull/214

**Full Changelog**: https://github.com/spatie/image/compare/3.1.0...3.2.0

## 3.1.0 - 2023-12-15

### What's Changed

* add Avif Support by @ariaieboy in https://github.com/spatie/image/pull/207

**Full Changelog**: https://github.com/spatie/image/compare/3.0.0...3.1.0

## 3.0.0 - 2023-12-14

### What's Changed

- do not rely on Glide anymore

### New Contributors

* @erikn69 made their first contribution in https://github.com/spatie/image/pull/197
* @timvandijck made their first contribution in https://github.com/spatie/image/pull/199

**Full Changelog**: https://github.com/spatie/image/compare/2.2.7...3.0.0

## 2.2.7 - 2023-07-24

- bump requirements

## 2.2.6 - 2023-05-06

### What's Changed

- Bump dependabot/fetch-metadata from 1.3.5 to 1.3.6 by @dependabot in https://github.com/spatie/image/pull/185
- Bump dependabot/fetch-metadata from 1.3.6 to 1.4.0 by @dependabot in https://github.com/spatie/image/pull/188
- Fit with only width or height by @gdebrauwer in https://github.com/spatie/image/pull/190

### New Contributors

- @dependabot made their first contribution in https://github.com/spatie/image/pull/185
- @gdebrauwer made their first contribution in https://github.com/spatie/image/pull/190

**Full Changelog**: https://github.com/spatie/image/compare/2.2.5...2.2.6

## 2.2.5 - 2023-01-19

### What's Changed

- Refactor tests to pest by @AyoobMH in https://github.com/spatie/image/pull/176
- Add Dependabot Automation by @patinthehat in https://github.com/spatie/image/pull/177
- Add PHP 8.2 Support by @patinthehat in https://github.com/spatie/image/pull/180
- Update Dependabot Automation by @patinthehat in https://github.com/spatie/image/pull/181
- Add fill-max fit mode by @Tofandel in https://github.com/spatie/image/pull/183

### New Contributors

- @AyoobMH made their first contribution in https://github.com/spatie/image/pull/176
- @patinthehat made their first contribution in https://github.com/spatie/image/pull/177
- @Tofandel made their first contribution in https://github.com/spatie/image/pull/183

**Full Changelog**: https://github.com/spatie/image/compare/2.2.4...2.2.5

## 2.2.4 - 2022-08-09

### What's Changed

- Add zero orientation support ignoring EXIF by @danielcastrobalbi in https://github.com/spatie/image/pull/171

### New Contributors

- @danielcastrobalbi made their first contribution in https://github.com/spatie/image/pull/171

**Full Changelog**: https://github.com/spatie/image/compare/2.2.3...2.2.4

## 2.2.3 - 2022-05-21

## What's Changed

- Fix permission issue with temporary directory by @sebastianpopp in https://github.com/spatie/image/pull/163

## New Contributors

- @sebastianpopp made their first contribution in https://github.com/spatie/image/pull/163

**Full Changelog**: https://github.com/spatie/image/compare/2.2.2...2.2.3

## 2.2.2 - 2022-02-22

- add TIFF support

## 1.11.0 - 2022-02-21

## What's Changed

- Fix docs link by @pascalbaljet in https://github.com/spatie/image/pull/154
- Update .gitattributes by @PaolaRuby in https://github.com/spatie/image/pull/158
- Add TIFF support by @Synchro in https://github.com/spatie/image/pull/159

## New Contributors

- @PaolaRuby made their first contribution in https://github.com/spatie/image/pull/158

**Full Changelog**: https://github.com/spatie/image/compare/2.2.1...1.11.0

## 2.2.1 - 2021-12-17

## What's Changed

- Use match expression in convertToGlideParameter method  by @mohprilaksono in https://github.com/spatie/image/pull/149
- [REF] updated fit docs description by @JeremyRed in https://github.com/spatie/image/pull/150
- Adding compatibility to Symfony 6 by @spackmat in https://github.com/spatie/image/pull/152

## New Contributors

- @mohprilaksono made their first contribution in https://github.com/spatie/image/pull/149
- @JeremyRed made their first contribution in https://github.com/spatie/image/pull/150
- @spackmat made their first contribution in https://github.com/spatie/image/pull/152

**Full Changelog**: https://github.com/spatie/image/compare/2.2.0...2.2.1

## 2.2.0 - 2021-10-31

- add avif support (#148)

## 2.1.0 - 2021-07-15

- Drop support for PHP 7
- Make codebase more strict with type hinting

## 2.0.0 - 2021-07-15

- Bump league/glide to v2 [#134](https://github.com/spatie/image/pull/134)

## 1.10.4 - 2021-04-07

- Allow spatie/temporary-directory v2

## 1.10.3 - 2021-03-10

- Bump league/glide to 2.0 [#123](https://github.com/spatie/image/pull/123)

## 1.10.2 - 2020-01-26

- change condition to delete $conversionResultDirectory (#118)

## 1.10.1 - 2020-12-27

- adds zoom option to focalCrop (#112)

## 1.9.0 - 2020-11-13

- allow usage of a custom `OptimizerChain` #110

## 1.8.1 - 2020-11-12

- revert changes from 1.8.0

## 1.8.0 - 2020-11-12

- allow usage of a custom `OptimizerChain` (#108)

## 1.7.7 - 2020-11-12

- add support for PHP 8

## 1.7.6 - 2020-01-26

- change uppercase function to mb_strtoupper instead of strtoupper (#99)

## 1.7.5 - 2019-11-23

- allow symfony 5 components

## 1.7.4 - 2019-08-28

- do not export docs

## 1.7.3 - 2019-08-03

- fix duplicated files (fixes #84)

## 1.7.2 - 2019-05-13

- fixes `optimize()` when used with `apply()` (#78)

## 1.7.1 - 2019-04-17

- change GlideConversion sequence (#76)

## 1.7.0 - 2019-02-22

- add support for `webp`

## 1.6.0 - 2019-01-27

- add `setTemporaryDirectory`

## 1.5.3 - 2019-01-10

- update lower deps

## 1.5.2 - 2018-05-05

- fix exception message

## 1.5.1 - 2018-04-18

- Prevent error when trying to remove `/tmp`

## 1.5.0 - 2018-04-13

- add `flip`

## 1.4.2 - 2018-04-11

- Use the correct driver for getting widths and height of images.

## 1.4.1 - 2018-02-08

- Support symfony ^4.0
- Support phpunit ^7.0

## 1.4.0 - 2017-12-05

- add `getWidth` and `getHeight`

## 1.3.5 - 2017-12-04

- fix for problems when creating directories in the temporary directory

## 1.3.4 - 2017-07-25

- fix `optimize` docblock

## 1.3.3 - 2017-07-11

- make `optimize` method fluent

## 1.3.2 - 2017-07-05

- swap out underlying optimization package

## 1.3.1 - 2017-07-02

- internally treat `optimize` as a manipulation

## 1.3.0 - 2017-07-02

- add `optimize` method

## 1.2.1 - 2017-06-29

- add methods to determine emptyness to `Manipulations` and `ManipulationSequence`

## 1.2.0 - 2017-04-17

- allow `Manipulations` to be constructed with an array of arrays

## 1.1.3 - 2017-04-07

- improve support for multi-volume systems

## 1.1.2 - 2017-04-04

- remove conversion directory after converting image

## 1.1.1 - 2017-03-17

- avoid processing empty manipulations groups

## 1.1.0 - 2017-02-06

- added support for watermarks

## 1.0.0 - 2017-02-06

- initial release
