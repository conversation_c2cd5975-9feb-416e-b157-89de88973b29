<?php $__env->startSection('title', 'Checkout - ' . $plan->name); ?>

<?php $__env->startSection('content'); ?>
<style>
    .checkout-container {
        max-width: 1000px;
        margin: 2rem auto;
        padding: 2rem;
    }

    .checkout-grid {
        display: grid;
        grid-template-columns: 1fr 400px;
        gap: 3rem;
    }

    .checkout-form {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .order-summary {
        background: var(--light-bg);
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        height: fit-content;
        position: sticky;
        top: 2rem;
    }

    .checkout-title {
        font-size: 2rem;
        color: var(--primary-color);
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #e1e5e9;
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        font-size: 1.3rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-color);
    }

    .form-input {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        font-size: 1rem;
        transition: border-color 0.3s;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--secondary-color);
    }

    .billing-cycle-options {
        display: grid;
        gap: 1rem;
    }

    .cycle-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .cycle-option:hover {
        border-color: var(--secondary-color);
    }

    .cycle-option.selected {
        border-color: var(--secondary-color);
        background: rgba(243, 156, 18, 0.1);
    }

    .cycle-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .cycle-price {
        font-weight: bold;
        color: var(--primary-color);
    }

    .cycle-savings {
        background: var(--secondary-color);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .payment-methods {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
    }

    .payment-method {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1rem;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .payment-method:hover {
        border-color: var(--secondary-color);
    }

    .payment-method.selected {
        border-color: var(--secondary-color);
        background: rgba(243, 156, 18, 0.1);
    }

    .payment-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: var(--secondary-color);
    }

    .card-element {
        padding: 1rem;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        background: white;
    }

    .summary-title {
        font-size: 1.5rem;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
    }

    .plan-info {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 1.5rem;
    }

    .plan-name {
        font-size: 1.3rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .plan-description {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }

    .plan-features {
        list-style: none;
        padding: 0;
    }

    .plan-features li {
        padding: 0.25rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
    }

    .plan-features i {
        color: var(--secondary-color);
        width: 12px;
    }

    .price-breakdown {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 1.5rem;
    }

    .price-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e1e5e9;
    }

    .price-row:last-child {
        border-bottom: none;
        font-weight: bold;
        font-size: 1.1rem;
        color: var(--primary-color);
    }

    .btn-checkout {
        width: 100%;
        padding: 1rem;
        font-size: 1.2rem;
        font-weight: bold;
        background: var(--secondary-color);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-checkout:hover {
        background: #e67e22;
    }

    .btn-checkout:disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .security-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #666;
        font-size: 0.9rem;
        margin-top: 1rem;
    }

    .security-info i {
        color: var(--secondary-color);
    }

    @media (max-width: 768px) {
        .checkout-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .order-summary {
            position: static;
        }
        
        .payment-methods {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>

<div class="container checkout-container">
    <div class="checkout-grid">
        <!-- Checkout Form -->
        <div class="checkout-form">
            <h1 class="checkout-title">Complete Your Purchase</h1>
            
            <form id="checkout-form" method="POST" action="<?php echo e(route('subscriptions.subscribe', $plan)); ?>">
                <?php echo csrf_field(); ?>
                
                <!-- Billing Cycle Selection -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-calendar-alt"></i>
                        Choose Billing Cycle
                    </h3>
                    
                    <div class="billing-cycle-options">
                        <?php if($plan->monthly_price): ?>
                        <div class="cycle-option <?php echo e($billingCycle === 'monthly' ? 'selected' : ''); ?>" 
                             onclick="selectBillingCycle('monthly', <?php echo e($plan->monthly_price); ?>)">
                            <div class="cycle-info">
                                <input type="radio" name="billing_cycle" value="monthly" 
                                       <?php echo e($billingCycle === 'monthly' ? 'checked' : ''); ?> style="display: none;">
                                <span>Monthly</span>
                            </div>
                            <div class="cycle-price">$<?php echo e(number_format($plan->monthly_price, 0)); ?>/month</div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if($plan->quarterly_price): ?>
                        <div class="cycle-option <?php echo e($billingCycle === 'quarterly' ? 'selected' : ''); ?>" 
                             onclick="selectBillingCycle('quarterly', <?php echo e($plan->quarterly_price); ?>)">
                            <div class="cycle-info">
                                <input type="radio" name="billing_cycle" value="quarterly" 
                                       <?php echo e($billingCycle === 'quarterly' ? 'checked' : ''); ?> style="display: none;">
                                <span>Quarterly</span>
                                <span class="cycle-savings">Save <?php echo e(round((($plan->monthly_price * 3) - $plan->quarterly_price) / ($plan->monthly_price * 3) * 100)); ?>%</span>
                            </div>
                            <div class="cycle-price">$<?php echo e(number_format($plan->quarterly_price, 0)); ?>/3 months</div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if($plan->yearly_price): ?>
                        <div class="cycle-option <?php echo e($billingCycle === 'yearly' ? 'selected' : ''); ?>" 
                             onclick="selectBillingCycle('yearly', <?php echo e($plan->yearly_price); ?>)">
                            <div class="cycle-info">
                                <input type="radio" name="billing_cycle" value="yearly" 
                                       <?php echo e($billingCycle === 'yearly' ? 'checked' : ''); ?> style="display: none;">
                                <span>Yearly</span>
                                <span class="cycle-savings">Save <?php echo e(round((($plan->monthly_price * 12) - $plan->yearly_price) / ($plan->monthly_price * 12) * 100)); ?>%</span>
                            </div>
                            <div class="cycle-price">$<?php echo e(number_format($plan->yearly_price, 0)); ?>/year</div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-credit-card"></i>
                        Payment Method
                    </h3>
                    
                    <div class="payment-methods">
                        <div class="payment-method selected" onclick="selectPaymentMethod('card')">
                            <input type="radio" name="payment_type" value="card" checked style="display: none;">
                            <div class="payment-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <span>Credit Card</span>
                        </div>
                        <div class="payment-method" onclick="selectPaymentMethod('crypto')">
                            <input type="radio" name="payment_type" value="crypto" style="display: none;">
                            <div class="payment-icon">
                                <i class="fab fa-bitcoin"></i>
                            </div>
                            <span>Crypto</span>
                        </div>
                    </div>
                </div>

                <!-- Card Details -->
                <div class="form-section" id="card-section">
                    <h3 class="section-title">
                        <i class="fas fa-lock"></i>
                        Card Details
                    </h3>
                    
                    <div class="form-group">
                        <label class="form-label">Card Information</label>
                        <div id="card-element" class="card-element">
                            <!-- Stripe Elements will create form elements here -->
                        </div>
                        <div id="card-errors" role="alert" style="color: #e74c3c; margin-top: 0.5rem;"></div>
                    </div>
                </div>

                <input type="hidden" name="payment_method" id="payment_method">
                
                <button type="submit" class="btn-checkout" id="submit-button">
                    <i class="fas fa-lock"></i>
                    Complete Purchase - $<span id="total-amount"><?php echo e(number_format($price, 0)); ?></span>
                </button>
                
                <div class="security-info">
                    <i class="fas fa-shield-alt"></i>
                    <span>Your payment information is secure and encrypted</span>
                </div>
            </form>
        </div>

        <!-- Order Summary -->
        <div class="order-summary">
            <h3 class="summary-title">Order Summary</h3>
            
            <div class="plan-info">
                <div class="plan-name"><?php echo e($plan->name); ?></div>
                <div class="plan-description"><?php echo e($plan->description); ?></div>
                
                <ul class="plan-features">
                    <?php $__currentLoopData = array_slice($plan->features, 0, 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <i class="fas fa-check"></i>
                        <span><?php echo e($feature); ?></span>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php if(count($plan->features) > 5): ?>
                    <li style="color: #666; font-style: italic;">
                        <i class="fas fa-plus"></i>
                        <span>And <?php echo e(count($plan->features) - 5); ?> more features...</span>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <div class="price-breakdown">
                <div class="price-row">
                    <span>Subtotal</span>
                    <span>$<span id="subtotal"><?php echo e(number_format($price, 0)); ?></span></span>
                </div>
                <div class="price-row">
                    <span>Tax</span>
                    <span>$0.00</span>
                </div>
                <div class="price-row">
                    <span>Total</span>
                    <span>$<span id="total"><?php echo e(number_format($price, 0)); ?></span></span>
                </div>
            </div>
            
            <div style="background: white; padding: 1rem; border-radius: 8px; text-align: center;">
                <i class="fas fa-medal" style="color: var(--secondary-color); font-size: 1.5rem; margin-bottom: 0.5rem;"></i>
                <div style="font-weight: bold; color: var(--primary-color); margin-bottom: 0.5rem;">30-Day Guarantee</div>
                <div style="font-size: 0.9rem; color: #666;">Not satisfied? Get a full refund within 30 days.</div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://js.stripe.com/v3/"></script>
<script>
    // Stripe setup (you'll need to add your publishable key)
    const stripe = Stripe('pk_test_your_stripe_publishable_key_here');
    const elements = stripe.elements();
    const cardElement = elements.create('card');
    cardElement.mount('#card-element');

    // Handle form submission
    const form = document.getElementById('checkout-form');
    const submitButton = document.getElementById('submit-button');

    form.addEventListener('submit', async (event) => {
        event.preventDefault();
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        const {token, error} = await stripe.createToken(cardElement);

        if (error) {
            document.getElementById('card-errors').textContent = error.message;
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-lock"></i> Complete Purchase - $' + document.getElementById('total-amount').textContent;
        } else {
            document.getElementById('payment_method').value = token.id;
            form.submit();
        }
    });

    // Billing cycle selection
    function selectBillingCycle(cycle, price) {
        document.querySelectorAll('.cycle-option').forEach(option => {
            option.classList.remove('selected');
        });
        event.currentTarget.classList.add('selected');
        
        document.querySelector(`input[value="${cycle}"]`).checked = true;
        
        // Update prices
        document.getElementById('subtotal').textContent = price.toLocaleString();
        document.getElementById('total').textContent = price.toLocaleString();
        document.getElementById('total-amount').textContent = price.toLocaleString();
    }

    // Payment method selection
    function selectPaymentMethod(method) {
        document.querySelectorAll('.payment-method').forEach(option => {
            option.classList.remove('selected');
        });
        event.currentTarget.classList.add('selected');
        
        document.querySelector(`input[value="${method}"]`).checked = true;
        
        // Show/hide card section
        const cardSection = document.getElementById('card-section');
        if (method === 'card') {
            cardSection.style.display = 'block';
        } else {
            cardSection.style.display = 'none';
        }
    }
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Work Space\THS\LMS\resources\views/subscriptions/checkout.blade.php ENDPATH**/ ?>