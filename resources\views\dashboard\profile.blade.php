@extends('layouts.app')

@section('title', 'Profile Settings')

@section('content')
<style>
    .profile-container {
        padding: 2rem 0;
    }

    .profile-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .profile-title {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .profile-subtitle {
        color: #666;
        font-size: 1.1rem;
    }

    .profile-grid {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 3rem;
        max-width: 1000px;
        margin: 0 auto;
    }

    .profile-sidebar {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        height: fit-content;
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, var(--secondary-color), #e67e22);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        margin: 0 auto 1.5rem;
        position: relative;
        cursor: pointer;
        transition: transform 0.3s;
    }

    .profile-avatar:hover {
        transform: scale(1.05);
    }

    .avatar-upload {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 35px;
        height: 35px;
        background: var(--primary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
        cursor: pointer;
    }

    .profile-name {
        text-align: center;
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .profile-email {
        text-align: center;
        color: #666;
        margin-bottom: 1.5rem;
    }

    .profile-stats {
        border-top: 1px solid #e1e5e9;
        padding-top: 1.5rem;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .stat-item:last-child {
        border-bottom: none;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }

    .stat-value {
        font-weight: bold;
        color: var(--primary-color);
    }

    .profile-form {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #e1e5e9;
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        font-size: 1.3rem;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-color);
    }

    .form-input,
    .form-textarea,
    .form-select {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        font-size: 1rem;
        transition: border-color 0.3s;
        font-family: inherit;
    }

    .form-input:focus,
    .form-textarea:focus,
    .form-select:focus {
        outline: none;
        border-color: var(--secondary-color);
    }

    .form-textarea {
        resize: vertical;
        min-height: 100px;
    }

    .form-error {
        color: var(--accent-color);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .btn-save {
        background: var(--secondary-color);
        color: white;
        padding: 1rem 2rem;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: bold;
        cursor: pointer;
        transition: background 0.3s;
    }

    .btn-save:hover {
        background: #e67e22;
    }

    .btn-secondary {
        background: transparent;
        color: var(--primary-color);
        border: 2px solid #e1e5e9;
        padding: 1rem 2rem;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none;
        display: inline-block;
    }

    .btn-secondary:hover {
        border-color: var(--secondary-color);
        color: var(--secondary-color);
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
    }

    .social-links {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .file-input {
        display: none;
    }

    @media (max-width: 768px) {
        .profile-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .social-links {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="container profile-container">
    <!-- Header -->
    <div class="profile-header">
        <h1 class="profile-title">Profile Settings</h1>
        <p class="profile-subtitle">Manage your account information and preferences</p>
    </div>

    <div class="profile-grid">
        <!-- Sidebar -->
        <div class="profile-sidebar">
            <div class="profile-avatar" onclick="document.getElementById('avatar-input').click()">
                @if($user->avatar)
                    <img src="{{ $user->avatar_url }}" alt="Avatar" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                @else
                    <i class="fas fa-user"></i>
                @endif
                <div class="avatar-upload">
                    <i class="fas fa-camera"></i>
                </div>
            </div>
            
            <div class="profile-name">{{ $user->full_name }}</div>
            <div class="profile-email">{{ $user->email }}</div>
            
            <div class="profile-stats">
                <div class="stat-item">
                    <span class="stat-label">Member Since</span>
                    <span class="stat-value">{{ $user->created_at->format('M Y') }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Courses Enrolled</span>
                    <span class="stat-value">{{ $user->enrollments()->count() }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Courses Completed</span>
                    <span class="stat-value">{{ $user->enrollments()->where('status', 'completed')->count() }}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Last Login</span>
                    <span class="stat-value">{{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never' }}</span>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="profile-form">
            <form method="POST" action="{{ route('dashboard.profile.update') }}" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <!-- Hidden file input for avatar -->
                <input type="file" id="avatar-input" name="avatar" class="file-input" accept="image/*">
                
                <!-- Personal Information -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-user"></i>
                        Personal Information
                    </h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name" class="form-label">First Name</label>
                            <input type="text" id="first_name" name="first_name" class="form-input @error('first_name') error @enderror" 
                                   value="{{ old('first_name', $user->first_name) }}" required>
                            @error('first_name')
                                <div class="form-error">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="form-group">
                            <label for="last_name" class="form-label">Last Name</label>
                            <input type="text" id="last_name" name="last_name" class="form-input @error('last_name') error @enderror" 
                                   value="{{ old('last_name', $user->last_name) }}" required>
                            @error('last_name')
                                <div class="form-error">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" id="email" name="email" class="form-input @error('email') error @enderror" 
                               value="{{ old('email', $user->email) }}" required>
                        @error('email')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" id="phone" name="phone" class="form-input @error('phone') error @enderror" 
                               value="{{ old('phone', $user->phone) }}">
                        @error('phone')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Bio & Preferences -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fas fa-edit"></i>
                        Bio & Preferences
                    </h3>
                    
                    <div class="form-group">
                        <label for="bio" class="form-label">Bio</label>
                        <textarea id="bio" name="bio" class="form-textarea @error('bio') error @enderror" 
                                  placeholder="Tell us about yourself...">{{ old('bio', $user->bio) }}</textarea>
                        @error('bio')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="form-group">
                        <label for="timezone" class="form-label">Timezone</label>
                        <select id="timezone" name="timezone" class="form-select @error('timezone') error @enderror" required>
                            <option value="UTC" {{ old('timezone', $user->timezone) === 'UTC' ? 'selected' : '' }}>UTC</option>
                            <option value="America/New_York" {{ old('timezone', $user->timezone) === 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                            <option value="America/Chicago" {{ old('timezone', $user->timezone) === 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                            <option value="America/Denver" {{ old('timezone', $user->timezone) === 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                            <option value="America/Los_Angeles" {{ old('timezone', $user->timezone) === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                            <option value="Europe/London" {{ old('timezone', $user->timezone) === 'Europe/London' ? 'selected' : '' }}>London</option>
                            <option value="Europe/Paris" {{ old('timezone', $user->timezone) === 'Europe/Paris' ? 'selected' : '' }}>Paris</option>
                            <option value="Asia/Tokyo" {{ old('timezone', $user->timezone) === 'Asia/Tokyo' ? 'selected' : '' }}>Tokyo</option>
                        </select>
                        @error('timezone')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <a href="{{ route('dashboard') }}" class="btn-secondary">Cancel</a>
                    <button type="submit" class="btn-save">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    // Handle avatar upload preview
    document.getElementById('avatar-input').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const avatar = document.querySelector('.profile-avatar');
                avatar.innerHTML = `
                    <img src="${e.target.result}" alt="Avatar" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    <div class="avatar-upload">
                        <i class="fas fa-camera"></i>
                    </div>
                `;
            };
            reader.readAsDataURL(file);
        }
    });
</script>
@endpush
@endsection
