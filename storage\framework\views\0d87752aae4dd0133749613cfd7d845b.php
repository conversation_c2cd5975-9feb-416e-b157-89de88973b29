<?php $__env->startSection('title', 'Pricing Plans'); ?>

<?php $__env->startSection('content'); ?>
<style>
    .pricing-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
    }

    .pricing-hero h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .pricing-hero p {
        font-size: 1.2rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
    }

    .pricing-section {
        padding: 4rem 0;
    }

    .pricing-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .pricing-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        text-align: center;
        position: relative;
        transition: transform 0.3s, box-shadow 0.3s;
        border: 3px solid transparent;
    }

    .pricing-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .pricing-card.popular {
        border-color: var(--secondary-color);
        transform: scale(1.05);
    }

    .pricing-card.popular::before {
        content: "Most Popular";
        position: absolute;
        top: -15px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--secondary-color);
        color: white;
        padding: 0.5rem 1.5rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
    }

    .plan-name {
        font-size: 1.8rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .plan-description {
        color: #666;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .pricing-options {
        margin-bottom: 2rem;
    }

    .price-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        border: 2px solid #e1e5e9;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
    }

    .price-option:hover {
        border-color: var(--secondary-color);
    }

    .price-option.selected {
        border-color: var(--secondary-color);
        background: rgba(243, 156, 18, 0.1);
    }

    .price-period {
        font-weight: 500;
    }

    .price-amount {
        font-weight: bold;
        color: var(--primary-color);
    }

    .price-savings {
        font-size: 0.8rem;
        color: var(--secondary-color);
        font-weight: bold;
    }

    .features-list {
        text-align: left;
        margin-bottom: 2rem;
    }

    .features-list li {
        padding: 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .features-list i {
        color: var(--secondary-color);
        width: 16px;
    }

    .btn-plan {
        width: 100%;
        padding: 1rem;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .faq-section {
        background: var(--light-bg);
        padding: 4rem 0;
    }

    .faq-title {
        text-align: center;
        font-size: 2.5rem;
        margin-bottom: 3rem;
        color: var(--primary-color);
    }

    .faq-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
    }

    .faq-item {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .faq-question {
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .faq-answer {
        color: #666;
        line-height: 1.6;
    }

    .guarantee-section {
        background: var(--secondary-color);
        color: white;
        text-align: center;
        padding: 3rem 0;
    }

    .guarantee-section h2 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .pricing-grid {
            grid-template-columns: 1fr;
        }
        
        .pricing-card.popular {
            transform: none;
        }
        
        .faq-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<!-- Hero Section -->
<section class="pricing-hero">
    <div class="container">
        <h1>Choose Your Path to Success</h1>
        <p>Join thousands of students who are already transforming their lives. Pick the plan that fits your goals and budget.</p>
    </div>
</section>

<!-- Pricing Section -->
<section class="pricing-section">
    <div class="container">
        <div class="pricing-grid">
            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="pricing-card <?php echo e($plan->is_popular ? 'popular' : ''); ?>">
                <div class="plan-name"><?php echo e($plan->name); ?></div>
                <div class="plan-description"><?php echo e($plan->description); ?></div>
                
                <div class="pricing-options">
                    <?php if($plan->monthly_price): ?>
                    <div class="price-option" data-period="monthly" data-price="<?php echo e($plan->monthly_price); ?>">
                        <div class="price-period">Monthly</div>
                        <div>
                            <div class="price-amount">$<?php echo e(number_format($plan->monthly_price, 0)); ?>/mo</div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if($plan->quarterly_price): ?>
                    <div class="price-option" data-period="quarterly" data-price="<?php echo e($plan->quarterly_price); ?>">
                        <div class="price-period">Quarterly</div>
                        <div>
                            <div class="price-amount">$<?php echo e(number_format($plan->quarterly_price, 0)); ?>/3mo</div>
                            <div class="price-savings">Save <?php echo e(round((($plan->monthly_price * 3) - $plan->quarterly_price) / ($plan->monthly_price * 3) * 100)); ?>%</div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if($plan->yearly_price): ?>
                    <div class="price-option selected" data-period="yearly" data-price="<?php echo e($plan->yearly_price); ?>">
                        <div class="price-period">Yearly</div>
                        <div>
                            <div class="price-amount">$<?php echo e(number_format($plan->yearly_price, 0)); ?>/year</div>
                            <div class="price-savings">Save <?php echo e(round((($plan->monthly_price * 12) - $plan->yearly_price) / ($plan->monthly_price * 12) * 100)); ?>%</div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <ul class="features-list">
                    <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <i class="fas fa-check"></i>
                        <span><?php echo e($feature); ?></span>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>

                <?php if(auth()->guard()->check()): ?>
                    <a href="<?php echo e(route('subscriptions.checkout', $plan)); ?>?period=yearly" class="btn btn-primary btn-plan">
                        Get Started
                    </a>
                <?php else: ?>
                    <a href="<?php echo e(route('register')); ?>" class="btn btn-primary btn-plan">
                        Get Started
                    </a>
                <?php endif; ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>

<!-- Money Back Guarantee -->
<section class="guarantee-section">
    <div class="container">
        <h2><i class="fas fa-shield-alt"></i> 30-Day Money Back Guarantee</h2>
        <p>Try The Real World risk-free. If you're not completely satisfied within 30 days, we'll refund every penny.</p>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section">
    <div class="container">
        <h2 class="faq-title">Frequently Asked Questions</h2>
        <div class="faq-grid">
            <div class="faq-item">
                <div class="faq-question">What's included in my membership?</div>
                <div class="faq-answer">Access to all courses, live mentorship calls, exclusive community, downloadable resources, and ongoing support from our team of experts.</div>
            </div>
            <div class="faq-item">
                <div class="faq-question">Can I cancel anytime?</div>
                <div class="faq-answer">Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your billing period.</div>
            </div>
            <div class="faq-item">
                <div class="faq-question">Do you offer refunds?</div>
                <div class="faq-answer">We offer a 30-day money-back guarantee. If you're not satisfied, contact us within 30 days for a full refund.</div>
            </div>
            <div class="faq-item">
                <div class="faq-question">How do I access the content?</div>
                <div class="faq-answer">Once you subscribe, you'll get instant access to your member dashboard where all courses and resources are available 24/7.</div>
            </div>
            <div class="faq-item">
                <div class="faq-question">Is there a free trial?</div>
                <div class="faq-answer">We don't offer free trials, but we do offer a 30-day money-back guarantee so you can try everything risk-free.</div>
            </div>
            <div class="faq-item">
                <div class="faq-question">What payment methods do you accept?</div>
                <div class="faq-answer">We accept all major credit cards and cryptocurrency payments (USDT, USDC) for your convenience.</div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('scripts'); ?>
<script>
    // Handle price option selection
    document.querySelectorAll('.price-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from siblings
            this.parentNode.querySelectorAll('.price-option').forEach(sibling => {
                sibling.classList.remove('selected');
            });
            // Add selected class to clicked option
            this.classList.add('selected');
        });
    });

    // Subscribe to plan function
    function subscribeToPlan(planId, period) {
        // This would typically redirect to a checkout page or open a payment modal
        window.location.href = `/subscriptions/${planId}/subscribe?period=${period}`;
    }
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Work Space\THS\LMS\resources\views/pricing.blade.php ENDPATH**/ ?>