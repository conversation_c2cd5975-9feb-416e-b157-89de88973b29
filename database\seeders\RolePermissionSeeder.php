<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Course management
            'view courses',
            'create courses',
            'edit courses',
            'delete courses',
            'publish courses',

            // Lesson management
            'view lessons',
            'create lessons',
            'edit lessons',
            'delete lessons',

            // Category management
            'view categories',
            'create categories',
            'edit categories',
            'delete categories',

            // Subscription management
            'view subscriptions',
            'manage subscriptions',
            'view subscription plans',
            'manage subscription plans',

            // Live events
            'view live events',
            'create live events',
            'edit live events',
            'delete live events',
            'host live events',

            // Forum management
            'view forums',
            'create forum posts',
            'edit forum posts',
            'delete forum posts',
            'moderate forums',

            // Chat management
            'view chat',
            'send messages',
            'moderate chat',

            // Admin panel
            'access admin panel',
            'view analytics',
            'manage settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Admin role - full access
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        // Mentor role - can create and manage courses, host events
        $mentorRole = Role::firstOrCreate(['name' => 'mentor']);
        $mentorRole->givePermissionTo([
            'view courses',
            'create courses',
            'edit courses',
            'view lessons',
            'create lessons',
            'edit lessons',
            'view live events',
            'create live events',
            'edit live events',
            'host live events',
            'view forums',
            'create forum posts',
            'edit forum posts',
            'view chat',
            'send messages',
        ]);

        // User role - basic access
        $userRole = Role::firstOrCreate(['name' => 'user']);
        $userRole->givePermissionTo([
            'view courses',
            'view lessons',
            'view live events',
            'view forums',
            'create forum posts',
            'view chat',
            'send messages',
        ]);
    }
}
