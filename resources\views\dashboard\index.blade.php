@extends('layouts.app')

@section('title', 'Dashboard')

@section('content')
<style>
    .dashboard-container {
        padding: 2rem 0;
    }

    .dashboard-header {
        margin-bottom: 2rem;
    }

    .dashboard-title {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .dashboard-subtitle {
        color: #666;
        font-size: 1.1rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
    }

    .stat-icon {
        font-size: 2.5rem;
        color: var(--secondary-color);
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }

    .dashboard-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
    }

    .dashboard-section {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .section-title {
        font-size: 1.5rem;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .course-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        margin-bottom: 1rem;
        transition: all 0.3s;
    }

    .course-item:hover {
        border-color: var(--secondary-color);
        transform: translateY(-2px);
    }

    .course-thumbnail {
        width: 60px;
        height: 60px;
        background: var(--light-bg);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: var(--secondary-color);
    }

    .course-info h4 {
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }

    .course-info p {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .progress-bar {
        width: 100%;
        height: 6px;
        background: #e1e5e9;
        border-radius: 3px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: var(--secondary-color);
        transition: width 0.3s;
    }

    .notification-item {
        padding: 1rem;
        border-left: 4px solid var(--secondary-color);
        background: var(--light-bg);
        margin-bottom: 1rem;
        border-radius: 0 8px 8px 0;
    }

    .notification-title {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .notification-message {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .notification-time {
        color: #999;
        font-size: 0.8rem;
    }

    .empty-state {
        text-align: center;
        color: #666;
        padding: 2rem;
    }

    .empty-state i {
        font-size: 3rem;
        color: #ccc;
        margin-bottom: 1rem;
    }

    .achievements-section {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .achievements-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .achievement-card {
        background: var(--light-bg);
        padding: 1.5rem;
        border-radius: 8px;
        text-align: center;
        border: 2px solid transparent;
        transition: all 0.3s;
    }

    .achievement-card.earned {
        border-color: var(--secondary-color);
        background: rgba(243, 156, 18, 0.1);
    }

    .achievement-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: block;
    }

    .achievement-name {
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
    }

    .achievement-description {
        font-size: 0.9rem;
        color: #666;
    }

    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }

        .achievements-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }
    }
</style>

<div class="container dashboard-container">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">Welcome back, {{ $user->first_name }}!</h1>
        <p class="dashboard-subtitle">Continue your journey to financial freedom</p>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-book-open"></i>
            </div>
            <div class="stat-number">{{ $totalEnrolledCourses }}</div>
            <div class="stat-label">Enrolled Courses</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="stat-number">{{ $completedCourses }}</div>
            <div class="stat-label">Completed Courses</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-number">{{ $progressPercentage }}%</div>
            <div class="stat-label">Overall Progress</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-fire"></i>
            </div>
            <div class="stat-number">{{ $learningStreak }}</div>
            <div class="stat-label">Day Streak</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-number">{{ floor($totalWatchTime / 3600) }}h</div>
            <div class="stat-label">Watch Time</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calendar"></i>
            </div>
            <div class="stat-number">{{ $upcomingEvents->count() }}</div>
            <div class="stat-label">Upcoming Events</div>
        </div>
    </div>

    <!-- Achievements Section -->
    @if(count($achievements) > 0)
    <div class="achievements-section">
        <h2 class="section-title">
            <i class="fas fa-medal"></i>
            Your Achievements
        </h2>
        <div class="achievements-grid">
            @foreach($achievements as $achievement)
            <div class="achievement-card earned">
                <i class="{{ $achievement['icon'] }} achievement-icon" style="color: {{ $achievement['color'] }};"></i>
                <div class="achievement-name">{{ $achievement['name'] }}</div>
                <div class="achievement-description">{{ $achievement['description'] }}</div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Recent Courses -->
        <div class="dashboard-section">
            <h2 class="section-title">
                <i class="fas fa-play-circle"></i>
                Continue Learning
            </h2>

            @if($enrolledCourses->count() > 0)
                @foreach($enrolledCourses as $enrollment)
                <div class="course-item">
                    <div class="course-thumbnail">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="course-info">
                        <h4>{{ $enrollment->course->title }}</h4>
                        <p>by {{ $enrollment->course->instructor->full_name }}</p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {{ $enrollment->progress_percentage }}%"></div>
                        </div>
                    </div>
                </div>
                @endforeach

                <div style="text-align: center; margin-top: 1.5rem;">
                    <a href="{{ route('dashboard.courses') }}" class="btn btn-primary">View All Courses</a>
                </div>
            @else
                <div class="empty-state">
                    <i class="fas fa-book-open"></i>
                    <p>You haven't enrolled in any courses yet.</p>
                    <a href="{{ route('home') }}" class="btn btn-primary">Browse Courses</a>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div>
            <!-- Notifications -->
            <div class="dashboard-section" style="margin-bottom: 2rem;">
                <h3 class="section-title">
                    <i class="fas fa-bell"></i>
                    Recent Updates
                </h3>

                @if($notifications->count() > 0)
                    @foreach($notifications as $notification)
                    <div class="notification-item">
                        <div class="notification-title">{{ $notification->title }}</div>
                        <div class="notification-message">{{ $notification->message }}</div>
                        <div class="notification-time">{{ $notification->created_at->diffForHumans() }}</div>
                    </div>
                    @endforeach
                @else
                    <div class="empty-state">
                        <i class="fas fa-bell-slash"></i>
                        <p>No new notifications</p>
                    </div>
                @endif
            </div>

            <!-- Upcoming Events -->
            @if($upcomingEvents->count() > 0)
            <div class="dashboard-section">
                <h3 class="section-title">
                    <i class="fas fa-calendar-alt"></i>
                    Upcoming Events
                </h3>

                @foreach($upcomingEvents as $event)
                <div class="notification-item">
                    <div class="notification-title">{{ $event->title }}</div>
                    <div class="notification-message">{{ $event->description }}</div>
                    <div class="notification-time">{{ $event->scheduled_at->format('M j, Y \a\t g:i A') }}</div>
                </div>
                @endforeach
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
