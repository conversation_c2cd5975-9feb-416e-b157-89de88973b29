<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Testimonial;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'name' => '<PERSON>',
                'title' => 'Crypto Trader',
                'content' => 'I went from complete beginner to making $15k/month in crypto trading within 6 months. The strategies taught here actually work!',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => '<PERSON>',
                'title' => 'E-commerce Entrepreneur',
                'content' => 'The Real World changed my life. I built a 6-figure dropshipping business using the exact methods taught in the courses.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => '<PERSON>',
                'title' => 'Copywriter',
                'content' => 'The copywriting course alone paid for itself 10x over. I now charge $5k per sales page and have clients lined up.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => '<PERSON> <PERSON>',
                'title' => 'Digital Marketer',
                'content' => 'From struggling freelancer to running my own agency. The mentorship and community support here is unmatched.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'David Thompson',
                'title' => 'Stock Trader',
                'content' => 'Finally found a trading strategy that works consistently. Made back my investment in the first month.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Lisa Park',
                'title' => 'AI Automation Specialist',
                'content' => 'The AI automation course opened up a whole new income stream for me. Now I help businesses save thousands with AI.',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($testimonials as $testimonialData) {
            Testimonial::firstOrCreate(
                ['name' => $testimonialData['name']],
                $testimonialData
            );
        }
    }
}
