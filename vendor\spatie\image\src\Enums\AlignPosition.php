<?php

namespace Spatie\Image\Enums;

enum AlignPosition: string
{
    case Top = 'top';
    case TopCenter = 'top-center';
    case TopMiddle = 'top-middle';
    case CenterTop = 'center-top';
    case MiddleTop = 'middle-top';
    case TopRight = 'top-right';
    case RightTop = 'right-top';
    case Left = 'left';
    case LeftCenter = 'left-center';
    case LeftMiddle = 'left-middle';
    case CenterLeft = 'center-left';
    case MiddleLeft = 'middle-left';
    case Right = 'right';
    case RightCenter = 'right-center';
    case RightMiddle = 'right-middle';
    case CenterRight = 'center-right';
    case MiddleRight = 'middle-right';
    case BottomLeft = 'bottom-left';
    case LeftBottom = 'left-bottom';
    case Bottom = 'bottom';
    case BottomCenter = 'bottomCenter';
    case BottomMiddle = 'bottom-middle';
    case CenterBottom = 'center-bottom';
    case MiddleBottom = 'middle-bottom';
    case BottomRight = 'bottom-right';
    case RightBottom = 'right-bottom';
    case Center = 'center';
    case Middle = 'middle';
    case CenterCenter = 'centerCenter';
    case MiddleMiddle = 'middleMiddle';
    case TopLeft = 'top-left';
    case LeftTop = 'left-top';

}
