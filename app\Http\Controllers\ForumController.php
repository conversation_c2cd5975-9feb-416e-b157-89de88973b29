<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Forum;
use App\Models\ForumPost;
use App\Models\Category;
use Illuminate\Support\Facades\Auth;

class ForumController extends Controller
{
    /**
     * Display a listing of forums.
     */
    public function index()
    {
        $forums = Forum::with(['category', 'latestPost.user'])
            ->withCount(['posts', 'posts as recent_posts_count' => function($query) {
                $query->where('created_at', '>', now()->subDays(7));
            }])
            ->orderBy('sort_order')
            ->get()
            ->groupBy('category.name');

        $totalPosts = ForumPost::count();
        $totalMembers = \App\Models\User::count();
        $onlineMembers = \App\Models\User::where('last_login_at', '>', now()->subMinutes(15))->count();

        return view('forums.index', compact('forums', 'totalPosts', 'totalMembers', 'onlineMembers'));
    }

    /**
     * Display the specified forum.
     */
    public function show(Request $request, Forum $forum)
    {
        $query = $forum->posts()->with(['user', 'replies.user'])->withCount('replies');

        // Filter by search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Sort posts
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'popular':
                $query->orderBy('replies_count', 'desc');
                break;
            case 'title':
                $query->orderBy('title', 'asc');
                break;
            default:
                $query->orderBy('is_pinned', 'desc')
                      ->orderBy('updated_at', 'desc');
        }

        $posts = $query->paginate(20);

        return view('forums.show', compact('forum', 'posts'));
    }

    /**
     * Show the form for creating a new post.
     */
    public function create(Forum $forum)
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to create posts.');
        }

        return view('forums.create-post', compact('forum'));
    }

    /**
     * Store a newly created post.
     */
    public function store(Request $request, Forum $forum)
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to create posts.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string|min:10',
        ]);

        $post = ForumPost::create([
            'forum_id' => $forum->id,
            'user_id' => Auth::id(),
            'title' => $request->title,
            'content' => $request->content,
        ]);

        return redirect()->route('forums.posts.show', [$forum, $post])
            ->with('success', 'Post created successfully!');
    }
}
