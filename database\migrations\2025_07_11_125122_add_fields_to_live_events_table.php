<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('live_events', function (Blueprint $table) {
            $table->string('slug')->unique()->after('title');
            $table->string('timezone')->default('UTC')->after('duration_minutes');
            $table->string('stream_url')->nullable()->after('timezone');
            $table->string('stream_platform')->nullable()->after('stream_url');
            $table->boolean('requires_subscription')->default(false)->after('stream_platform');
            $table->string('thumbnail')->nullable()->after('requires_subscription');
            $table->json('agenda')->nullable()->after('thumbnail');
            $table->json('resources')->nullable()->after('agenda');

            // Remove old columns that are not needed
            $table->dropColumn(['meeting_url', 'meeting_id', 'meeting_password', 'required_plans', 'is_recurring', 'recurrence_pattern']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('live_events', function (Blueprint $table) {
            $table->dropColumn(['slug', 'timezone', 'stream_url', 'stream_platform', 'requires_subscription', 'thumbnail', 'agenda', 'resources']);

            // Add back old columns
            $table->string('meeting_url')->nullable();
            $table->string('meeting_id')->nullable();
            $table->string('meeting_password')->nullable();
            $table->json('required_plans')->nullable();
            $table->boolean('is_recurring')->default(false);
            $table->string('recurrence_pattern')->nullable();
        });
    }
};
