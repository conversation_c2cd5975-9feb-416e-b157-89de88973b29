{"name": "spatie/image", "description": "Manipulate images with an expressive API", "keywords": ["spatie", "image"], "homepage": "https://github.com/spatie/image", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "require": {"php": "^8.2", "ext-exif": "*", "ext-json": "*", "ext-mbstring": "*", "spatie/image-optimizer": "^1.7.5", "spatie/temporary-directory": "^2.2", "symfony/process": "^6.4|^7.0"}, "require-dev": {"ext-gd": "*", "ext-imagick": "*", "laravel/sail": "^1.34", "pestphp/pest": "^2.28", "phpstan/phpstan": "^1.10.50", "spatie/pest-plugin-snapshots": "^2.1", "spatie/pixelmatch-php": "^1.0", "spatie/ray": "^1.40.1", "symfony/var-dumper": "^6.4|7.0"}, "autoload": {"psr-4": {"Spatie\\Image\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\Image\\Test\\": "tests"}}, "scripts": {"test": "vendor/bin/pest", "analyse": "phpstan analyse --memory-limit=1G --no-progress", "baseline": "./vendor/bin/phpstan analyse --memory-limit=2G --generate-baseline"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}}