<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ForumPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'forum_id',
        'user_id',
        'title',
        'content',
        'is_pinned',
        'is_locked',
        'views',
    ];

    protected $casts = [
        'is_pinned' => 'boolean',
        'is_locked' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function forum()
    {
        return $this->belongsTo(Forum::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function replies()
    {
        return $this->hasMany(ForumPostReply::class);
    }

    public function likes()
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    /**
     * Scopes
     */
    public function scopePinned($query)
    {
        return $query->where('is_pinned', true);
    }

    public function scopeNotLocked($query)
    {
        return $query->where('is_locked', false);
    }

    /**
     * Accessors
     */
    public function getRepliesCountAttribute()
    {
        return $this->replies()->count();
    }

    public function getLikesCountAttribute()
    {
        return $this->likes()->count();
    }

    public function getLatestReplyAttribute()
    {
        return $this->replies()->with('user')->latest()->first();
    }

    public function getExcerptAttribute()
    {
        return \Str::limit(strip_tags($this->content), 150);
    }

    public function getFormattedContentAttribute()
    {
        return nl2br(e($this->content));
    }

    /**
     * Check if user has liked this post
     */
    public function isLikedBy($user = null)
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return false;
        }

        return $this->likes()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if user can edit this post
     */
    public function canEdit($user = null)
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return false;
        }

        return $user->id === $this->user_id || $user->hasRole('admin');
    }

    /**
     * Check if user can delete this post
     */
    public function canDelete($user = null)
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return false;
        }

        return $user->id === $this->user_id || $user->hasRole('admin');
    }
}
