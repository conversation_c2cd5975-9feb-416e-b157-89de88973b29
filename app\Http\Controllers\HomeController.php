<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Category;
use App\Models\SubscriptionPlan;
use App\Models\Testimonial;

class HomeController extends Controller
{
    /**
     * Show the homepage.
     */
    public function index()
    {
        $featuredCourses = Course::published()
            ->featured()
            ->with(['instructor', 'category'])
            ->limit(6)
            ->get();

        $categories = Category::active()
            ->orderBy('sort_order')
            ->limit(8)
            ->get();

        $testimonials = Testimonial::active()
            ->featured()
            ->orderBy('sort_order')
            ->limit(6)
            ->get();

        return view('home', compact('featuredCourses', 'categories', 'testimonials'));
    }

    /**
     * Show the pricing page.
     */
    public function pricing()
    {
        $plans = SubscriptionPlan::active()
            ->orderBy('sort_order')
            ->get();

        return view('pricing', compact('plans'));
    }

    /**
     * Show the about page.
     */
    public function about()
    {
        return view('about');
    }

    /**
     * Show the contact page.
     */
    public function contact()
    {
        return view('contact');
    }

    /**
     * Handle contact form submission.
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Here you would typically send an email or store the message
        // For now, we'll just redirect back with a success message

        return redirect()->back()->with('success', 'Thank you for your message! We\'ll get back to you soon.');
    }
}
