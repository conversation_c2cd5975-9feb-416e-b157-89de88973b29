<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'monthly_price',
        'quarterly_price',
        'yearly_price',
        'stripe_monthly_price_id',
        'stripe_quarterly_price_id',
        'stripe_yearly_price_id',
        'features',
        'max_courses',
        'live_calls_access',
        'community_access',
        'mentor_access',
        'sort_order',
        'is_active',
        'is_popular',
    ];

    protected $casts = [
        'features' => 'array',
        'monthly_price' => 'decimal:2',
        'quarterly_price' => 'decimal:2',
        'yearly_price' => 'decimal:2',
        'live_calls_access' => 'boolean',
        'community_access' => 'boolean',
        'mentor_access' => 'boolean',
        'is_active' => 'boolean',
        'is_popular' => 'boolean',
    ];

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    /**
     * Relationships
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'stripe_price');
    }
}
