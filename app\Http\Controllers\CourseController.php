<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Category;
use App\Models\Enrollment;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    /**
     * Display a listing of courses.
     */
    public function index(Request $request)
    {
        $query = Course::published()->with(['instructor', 'category']);

        // Filter by category
        if ($request->has('category') && $request->category) {
            $query->where('category_id', $request->category);
        }

        // Filter by difficulty
        if ($request->has('difficulty') && $request->difficulty) {
            $query->where('difficulty_level', $request->difficulty);
        }

        // Filter by price
        if ($request->has('price_filter')) {
            if ($request->price_filter === 'free') {
                $query->where('is_free', true);
            } elseif ($request->price_filter === 'paid') {
                $query->where('is_free', false);
            }
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Sort
        $sortBy = $request->get('sort', 'latest');
        switch ($sortBy) {
            case 'popular':
                $query->withCount('enrollments')->orderBy('enrollments_count', 'desc');
                break;
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('price', 'desc');
                break;
            case 'alphabetical':
                $query->orderBy('title', 'asc');
                break;
            default:
                $query->orderBy('created_at', 'desc');
        }

        $courses = $query->paginate(12);
        $categories = Category::active()->orderBy('sort_order')->get();

        return view('courses.index', compact('courses', 'categories'));
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course)
    {
        if ($course->status !== 'published') {
            abort(404);
        }

        $course->load(['instructor', 'category', 'lessons' => function($query) {
            $query->where('is_published', true)->orderBy('sort_order');
        }]);

        $isEnrolled = false;
        $enrollment = null;
        $progress = 0;

        if (Auth::check()) {
            $enrollment = Auth::user()->enrollments()
                ->where('course_id', $course->id)
                ->first();

            if ($enrollment) {
                $isEnrolled = true;
                $progress = $enrollment->progress_percentage;
            }
        }

        // Get related courses
        $relatedCourses = Course::published()
            ->where('category_id', $course->category_id)
            ->where('id', '!=', $course->id)
            ->limit(4)
            ->get();

        return view('courses.show', compact('course', 'isEnrolled', 'enrollment', 'progress', 'relatedCourses'));
    }

    /**
     * Enroll user in a course.
     */
    public function enroll(Request $request, Course $course)
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Please login to enroll in courses.');
        }

        $user = Auth::user();

        // Check if user is already enrolled
        if ($user->enrollments()->where('course_id', $course->id)->exists()) {
            return redirect()->back()->with('info', 'You are already enrolled in this course.');
        }

        // Check if course is free or user has active subscription
        if (!$course->is_free && !$user->subscribed('default')) {
            return redirect()->route('pricing')->with('error', 'Please subscribe to access premium courses.');
        }

        // Create enrollment
        $enrollment = Enrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'enrolled_at' => now(),
        ]);

        return redirect()->route('courses.learn', $course)
            ->with('success', 'Successfully enrolled! Start learning now.');
    }

    /**
     * Show course learning interface.
     */
    public function learn(Course $course)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();

        if (!$enrollment) {
            return redirect()->route('courses.show', $course)
                ->with('error', 'You need to enroll in this course first.');
        }

        $course->load(['lessons' => function($query) {
            $query->where('is_published', true)->orderBy('sort_order');
        }]);

        // Get user's lesson progress
        $lessonProgress = $user->lessonProgress()
            ->whereIn('lesson_id', $course->lessons->pluck('id'))
            ->get()
            ->keyBy('lesson_id');

        // Find current lesson (first incomplete or first lesson)
        $currentLesson = $course->lessons->first(function($lesson) use ($lessonProgress) {
            return !isset($lessonProgress[$lesson->id]) || !$lessonProgress[$lesson->id]->is_completed;
        }) ?? $course->lessons->first();

        return view('courses.learn', compact('course', 'enrollment', 'lessonProgress', 'currentLesson'));
    }
}
