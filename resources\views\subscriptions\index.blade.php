@extends('layouts.app')

@section('title', 'Subscription Management')

@section('content')
<style>
    .subscription-container {
        padding: 2rem 0;
    }

    .subscription-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .subscription-title {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .subscription-subtitle {
        color: #666;
        font-size: 1.1rem;
    }

    .current-plan {
        background: linear-gradient(135deg, var(--secondary-color), #e67e22);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 3rem;
        text-align: center;
    }

    .current-plan h3 {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }

    .current-plan p {
        opacity: 0.9;
        margin-bottom: 1.5rem;
    }

    .plan-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .plan-detail {
        background: rgba(255,255,255,0.1);
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
    }

    .plan-detail-label {
        font-size: 0.9rem;
        opacity: 0.8;
        margin-bottom: 0.5rem;
    }

    .plan-detail-value {
        font-size: 1.2rem;
        font-weight: bold;
    }

    .plan-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-outline {
        background: transparent;
        border: 2px solid white;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.3s;
    }

    .btn-outline:hover {
        background: white;
        color: var(--secondary-color);
    }

    .no-subscription {
        text-align: center;
        padding: 3rem;
        background: var(--light-bg);
        border-radius: 15px;
        margin-bottom: 3rem;
    }

    .no-subscription i {
        font-size: 4rem;
        color: #ccc;
        margin-bottom: 1rem;
    }

    .no-subscription h3 {
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .available-plans {
        margin-top: 3rem;
    }

    .plans-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .plan-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        text-align: center;
        position: relative;
        transition: transform 0.3s;
        border: 3px solid transparent;
    }

    .plan-card:hover {
        transform: translateY(-5px);
    }

    .plan-card.popular {
        border-color: var(--secondary-color);
    }

    .plan-card.popular::before {
        content: "Most Popular";
        position: absolute;
        top: -15px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--secondary-color);
        color: white;
        padding: 0.5rem 1.5rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: bold;
    }

    .plan-name {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .plan-price {
        font-size: 2rem;
        font-weight: bold;
        color: var(--secondary-color);
        margin-bottom: 1rem;
    }

    .plan-features {
        text-align: left;
        margin-bottom: 2rem;
    }

    .plan-features li {
        padding: 0.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .plan-features i {
        color: var(--secondary-color);
        width: 16px;
    }

    .btn-plan {
        width: 100%;
        padding: 1rem;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 8px;
    }

    .billing-history {
        margin-top: 3rem;
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .billing-history h3 {
        color: var(--primary-color);
        margin-bottom: 1.5rem;
    }

    .billing-table {
        width: 100%;
        border-collapse: collapse;
    }

    .billing-table th,
    .billing-table td {
        padding: 1rem;
        text-align: left;
        border-bottom: 1px solid #e1e5e9;
    }

    .billing-table th {
        background: var(--light-bg);
        font-weight: bold;
        color: var(--primary-color);
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .status-paid {
        background: #d4edda;
        color: #155724;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
    }

    .status-failed {
        background: #f8d7da;
        color: #721c24;
    }

    @media (max-width: 768px) {
        .plans-grid {
            grid-template-columns: 1fr;
        }
        
        .plan-actions {
            flex-direction: column;
        }
        
        .billing-table {
            font-size: 0.9rem;
        }
    }
</style>

<div class="container subscription-container">
    <!-- Header -->
    <div class="subscription-header">
        <h1 class="subscription-title">Subscription Management</h1>
        <p class="subscription-subtitle">Manage your membership and billing preferences</p>
    </div>

    @if($currentSubscription && $currentSubscription->active())
        <!-- Current Subscription -->
        <div class="current-plan">
            <h3>Your Current Plan</h3>
            <p>You're currently subscribed to our premium content and community</p>
            
            <div class="plan-details">
                <div class="plan-detail">
                    <div class="plan-detail-label">Plan</div>
                    <div class="plan-detail-value">{{ $currentSubscription->name ?? 'Premium' }}</div>
                </div>
                <div class="plan-detail">
                    <div class="plan-detail-label">Status</div>
                    <div class="plan-detail-value">
                        @if($currentSubscription->cancelled())
                            Cancelled (Active until {{ $currentSubscription->ends_at->format('M j, Y') }})
                        @else
                            Active
                        @endif
                    </div>
                </div>
                <div class="plan-detail">
                    <div class="plan-detail-label">Next Billing</div>
                    <div class="plan-detail-value">
                        @if($currentSubscription->cancelled())
                            N/A
                        @else
                            {{ $currentSubscription->asStripeSubscription()->current_period_end ? \Carbon\Carbon::createFromTimestamp($currentSubscription->asStripeSubscription()->current_period_end)->format('M j, Y') : 'N/A' }}
                        @endif
                    </div>
                </div>
                <div class="plan-detail">
                    <div class="plan-detail-label">Amount</div>
                    <div class="plan-detail-value">${{ number_format($currentSubscription->asStripeSubscription()->items->data[0]->price->unit_amount / 100, 2) }}</div>
                </div>
            </div>

            <div class="plan-actions">
                <a href="{{ route('billing-portal') }}" class="btn-outline">
                    <i class="fas fa-credit-card"></i> Manage Billing
                </a>
                
                @if($currentSubscription->cancelled())
                    <form method="POST" action="{{ route('subscriptions.resume') }}" style="display: inline;">
                        @csrf
                        <button type="submit" class="btn-outline">
                            <i class="fas fa-play"></i> Resume Subscription
                        </button>
                    </form>
                @else
                    <form method="POST" action="{{ route('subscriptions.cancel') }}" style="display: inline;" 
                          onsubmit="return confirm('Are you sure you want to cancel your subscription?')">
                        @csrf
                        <button type="submit" class="btn-outline">
                            <i class="fas fa-times"></i> Cancel Subscription
                        </button>
                    </form>
                @endif
            </div>
        </div>
    @else
        <!-- No Subscription -->
        <div class="no-subscription">
            <i class="fas fa-user-slash"></i>
            <h3>No Active Subscription</h3>
            <p>You don't have an active subscription. Choose a plan below to get started!</p>
        </div>
    @endif

    <!-- Available Plans -->
    <div class="available-plans">
        <h2 style="text-align: center; color: var(--primary-color); margin-bottom: 2rem;">
            @if($currentSubscription && $currentSubscription->active())
                Upgrade Your Plan
            @else
                Choose Your Plan
            @endif
        </h2>
        
        <div class="plans-grid">
            @foreach($plans as $plan)
            <div class="plan-card {{ $plan->is_popular ? 'popular' : '' }}">
                <div class="plan-name">{{ $plan->name }}</div>
                <div class="plan-price">${{ number_format($plan->monthly_price, 0) }}<small>/month</small></div>
                
                <ul class="plan-features">
                    @foreach($plan->features as $feature)
                    <li>
                        <i class="fas fa-check"></i>
                        <span>{{ $feature }}</span>
                    </li>
                    @endforeach
                </ul>

                @if($currentSubscription && $currentSubscription->active())
                    <button class="btn btn-primary btn-plan" onclick="upgradePlan('{{ $plan->id }}')">
                        Upgrade to {{ $plan->name }}
                    </button>
                @else
                    <a href="{{ route('pricing') }}" class="btn btn-primary btn-plan">
                        Choose {{ $plan->name }}
                    </a>
                @endif
            </div>
            @endforeach
        </div>
    </div>
</div>

@push('scripts')
<script>
    function upgradePlan(planId) {
        if (confirm('Are you sure you want to upgrade your plan?')) {
            // This would typically open a payment modal or redirect to upgrade flow
            window.location.href = `/subscriptions/${planId}/subscribe?upgrade=true`;
        }
    }
</script>
@endpush
@endsection
