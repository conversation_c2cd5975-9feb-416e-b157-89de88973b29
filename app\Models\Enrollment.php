<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Enrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'course_id',
        'status',
        'progress_percentage',
        'enrolled_at',
        'completed_at',
        'last_accessed_at',
    ];

    protected $casts = [
        'progress_percentage' => 'decimal:2',
        'enrolled_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Accessors
     */
    public function getIsCompletedAttribute()
    {
        return $this->status === 'completed';
    }

    public function getProgressBarWidthAttribute()
    {
        return min(100, max(0, $this->progress_percentage));
    }

    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'completed' => 'badge-success',
            'active' => 'badge-primary',
            'suspended' => 'badge-warning',
            default => 'badge-secondary',
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'completed' => 'Completed',
            'active' => 'In Progress',
            'suspended' => 'Suspended',
            default => 'Unknown',
        };
    }
}
