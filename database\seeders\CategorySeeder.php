<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Cryptocurrency',
                'slug' => 'cryptocurrency',
                'description' => 'Learn about Bitcoin, Ethereum, DeFi, and crypto trading strategies.',
                'icon' => 'bitcoin',
                'color' => '#f7931a',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Stock Trading',
                'slug' => 'stock-trading',
                'description' => 'Master the stock market with proven trading strategies.',
                'icon' => 'chart-line',
                'color' => '#28a745',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Copywriting',
                'slug' => 'copywriting',
                'description' => 'Write compelling copy that converts and sells.',
                'icon' => 'pen',
                'color' => '#6f42c1',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'E-commerce',
                'slug' => 'ecommerce',
                'description' => 'Build and scale profitable online stores.',
                'icon' => 'shopping-cart',
                'color' => '#fd7e14',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'name' => 'AI Automation',
                'slug' => 'ai-automation',
                'description' => 'Leverage AI tools to automate and scale your business.',
                'icon' => 'robot',
                'color' => '#20c997',
                'sort_order' => 5,
                'is_active' => true,
            ],
            [
                'name' => 'Fitness & Health',
                'slug' => 'fitness-health',
                'description' => 'Optimize your physical and mental performance.',
                'icon' => 'dumbbell',
                'color' => '#dc3545',
                'sort_order' => 6,
                'is_active' => true,
            ],
            [
                'name' => 'Real Estate',
                'slug' => 'real-estate',
                'description' => 'Invest in and profit from real estate opportunities.',
                'icon' => 'home',
                'color' => '#17a2b8',
                'sort_order' => 7,
                'is_active' => true,
            ],
            [
                'name' => 'Business Strategy',
                'slug' => 'business-strategy',
                'description' => 'Strategic thinking and business development.',
                'icon' => 'briefcase',
                'color' => '#6c757d',
                'sort_order' => 8,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }
    }
}
