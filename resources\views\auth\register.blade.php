@extends('layouts.app')

@section('title', 'Join The Real World')

@section('content')
<style>
    .auth-container {
        max-width: 500px;
        margin: 4rem auto;
        padding: 2rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .auth-title {
        text-align: center;
        font-size: 2rem;
        margin-bottom: 2rem;
        color: var(--primary-color);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-color);
    }

    .form-input {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        font-size: 1rem;
        transition: border-color 0.3s;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--secondary-color);
    }

    .form-error {
        color: var(--accent-color);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-checkbox {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .form-checkbox input {
        margin-top: 0.25rem;
    }

    .btn-full {
        width: 100%;
        padding: 0.75rem;
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .auth-links {
        text-align: center;
        margin-top: 1rem;
    }

    .auth-links a {
        color: var(--secondary-color);
        text-decoration: none;
    }

    .auth-links a:hover {
        text-decoration: underline;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
</style>

<div class="container">
    <div class="auth-container">
        <h1 class="auth-title">Join The Real World</h1>
        <p style="text-align: center; margin-bottom: 2rem; color: #666;">
            Start your journey to financial freedom today
        </p>

        <form method="POST" action="{{ route('register') }}">
            @csrf

            <div class="form-row">
                <div class="form-group">
                    <label for="first_name" class="form-label">First Name</label>
                    <input 
                        type="text" 
                        id="first_name" 
                        name="first_name" 
                        class="form-input @error('first_name') error @enderror" 
                        value="{{ old('first_name') }}" 
                        required 
                        autofocus
                    >
                    @error('first_name')
                        <div class="form-error">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="last_name" class="form-label">Last Name</label>
                    <input 
                        type="text" 
                        id="last_name" 
                        name="last_name" 
                        class="form-input @error('last_name') error @enderror" 
                        value="{{ old('last_name') }}" 
                        required
                    >
                    @error('last_name')
                        <div class="form-error">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div class="form-group">
                <label for="email" class="form-label">Email Address</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    class="form-input @error('email') error @enderror" 
                    value="{{ old('email') }}" 
                    required
                >
                @error('email')
                    <div class="form-error">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="form-input @error('password') error @enderror" 
                    required
                >
                @error('password')
                    <div class="form-error">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group">
                <label for="password_confirmation" class="form-label">Confirm Password</label>
                <input 
                    type="password" 
                    id="password_confirmation" 
                    name="password_confirmation" 
                    class="form-input" 
                    required
                >
            </div>

            <div class="form-group">
                <label class="form-checkbox">
                    <input type="checkbox" name="terms" {{ old('terms') ? 'checked' : '' }} required>
                    <span>I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a></span>
                </label>
                @error('terms')
                    <div class="form-error">{{ $message }}</div>
                @enderror
            </div>

            <button type="submit" class="btn btn-primary btn-full">
                Create Account
            </button>
        </form>

        <div class="auth-links">
            <p>Already have an account? <a href="{{ route('login') }}">Sign in here</a></p>
        </div>
    </div>
</div>
@endsection
