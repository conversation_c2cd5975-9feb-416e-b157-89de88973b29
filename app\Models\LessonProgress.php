<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LessonProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'lesson_id',
        'is_completed',
        'watch_time_seconds',
        'completion_percentage',
        'started_at',
        'completed_at',
        'last_accessed_at',
    ];

    protected $casts = [
        'is_completed' => 'boolean',
        'completion_percentage' => 'decimal:2',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Scopes
     */
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    public function scopeInProgress($query)
    {
        return $query->where('is_completed', false)
                    ->where('completion_percentage', '>', 0);
    }

    /**
     * Accessors
     */
    public function getFormattedWatchTimeAttribute()
    {
        if (!$this->watch_time_seconds) {
            return '0m';
        }

        $hours = floor($this->watch_time_seconds / 3600);
        $minutes = floor(($this->watch_time_seconds % 3600) / 60);
        $seconds = $this->watch_time_seconds % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm', $hours, $minutes);
        } elseif ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return sprintf('%ds', $seconds);
        }
    }

    public function getProgressBarWidthAttribute()
    {
        return min(100, max(0, $this->completion_percentage));
    }

    public function getStatusIconAttribute()
    {
        if ($this->is_completed) {
            return 'fas fa-check-circle text-success';
        } elseif ($this->completion_percentage > 0) {
            return 'fas fa-play-circle text-primary';
        } else {
            return 'far fa-circle text-muted';
        }
    }

    public function getStatusTextAttribute()
    {
        if ($this->is_completed) {
            return 'Completed';
        } elseif ($this->completion_percentage > 0) {
            return 'In Progress';
        } else {
            return 'Not Started';
        }
    }
}
