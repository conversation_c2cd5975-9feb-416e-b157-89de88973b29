<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Cadet, Challenger, Hero, Champion
            $table->string('slug')->unique();
            $table->text('description');
            $table->decimal('monthly_price', 8, 2);
            $table->decimal('quarterly_price', 8, 2)->nullable();
            $table->decimal('yearly_price', 8, 2)->nullable();
            $table->string('stripe_monthly_price_id')->nullable();
            $table->string('stripe_quarterly_price_id')->nullable();
            $table->string('stripe_yearly_price_id')->nullable();
            $table->json('features'); // Array of features
            $table->integer('max_courses')->nullable(); // null = unlimited
            $table->boolean('live_calls_access')->default(false);
            $table->boolean('community_access')->default(false);
            $table->boolean('mentor_access')->default(false);
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_popular')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
