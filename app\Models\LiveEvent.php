<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class LiveEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'category_id',
        'host_id',
        'scheduled_at',
        'duration_minutes',
        'timezone',
        'stream_url',
        'stream_platform',
        'max_attendees',
        'requires_subscription',
        'status',
        'thumbnail',
        'recording_url',
        'agenda',
        'resources',
    ];

    protected $casts = [
        'scheduled_at' => 'datetime',
        'requires_subscription' => 'boolean',
        'agenda' => 'array',
        'resources' => 'array',
    ];

    /**
     * Relationships
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function host()
    {
        return $this->belongsTo(User::class, 'host_id');
    }

    public function registrations()
    {
        return $this->hasMany(EventRegistration::class);
    }

    public function attendees()
    {
        return $this->belongsToMany(User::class, 'event_registrations')
                    ->withTimestamps()
                    ->withPivot(['attended', 'registered_at']);
    }

    /**
     * Scopes
     */
    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_at', '>', now());
    }

    public function scopeLive($query)
    {
        return $query->where('status', 'live');
    }

    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Accessors
     */
    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail) {
            return asset('storage/' . $this->thumbnail);
        }
        return asset('assets/images/event-placeholder.jpg');
    }

    public function getFormattedDateAttribute()
    {
        return $this->scheduled_at->format('M j, Y');
    }

    public function getFormattedTimeAttribute()
    {
        return $this->scheduled_at->format('g:i A T');
    }

    public function getFormattedDurationAttribute()
    {
        if (!$this->duration_minutes) {
            return 'TBD';
        }

        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm', $hours, $minutes);
        }

        return sprintf('%dm', $minutes);
    }

    public function getRegistrationCountAttribute()
    {
        return $this->registrations()->count();
    }

    public function getIsFullAttribute()
    {
        return $this->max_attendees && $this->registration_count >= $this->max_attendees;
    }

    public function getIsLiveAttribute()
    {
        return $this->status === 'live';
    }

    public function getIsUpcomingAttribute()
    {
        return $this->scheduled_at > now();
    }

    public function getIsPastAttribute()
    {
        return $this->scheduled_at < now();
    }

    public function getCanJoinAttribute()
    {
        $eventTime = Carbon::parse($this->scheduled_at);
        $now = now();

        // Can join 15 minutes before event starts
        return $now->gte($eventTime->subMinutes(15)) &&
               ($this->status === 'live' || $this->status === 'scheduled');
    }

    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'live' => 'badge-live',
            'scheduled' => 'badge-scheduled',
            'completed' => 'badge-completed',
            'cancelled' => 'badge-cancelled',
            default => 'badge-secondary',
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'live' => 'LIVE NOW',
            'scheduled' => 'Scheduled',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            default => 'Unknown',
        };
    }

    /**
     * Check if user can register for this event
     */
    public function canRegister($user = null)
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            return false;
        }

        // Check if event is full
        if ($this->is_full) {
            return false;
        }

        // Check if event is in the past
        if ($this->is_past) {
            return false;
        }

        // Check if user is already registered
        if ($user->eventRegistrations()->where('live_event_id', $this->id)->exists()) {
            return false;
        }

        // Check subscription requirement
        if ($this->requires_subscription && !$user->subscribed('default')) {
            return false;
        }

        return true;
    }
}
