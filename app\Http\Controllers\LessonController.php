<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\LessonProgress;
use Illuminate\Support\Facades\Auth;

class LessonController extends Controller
{
    /**
     * Show a specific lesson.
     */
    public function show(Course $course, Lesson $lesson)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Check if user is enrolled in the course
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();
        if (!$enrollment) {
            return redirect()->route('courses.show', $course)
                ->with('error', 'You need to enroll in this course first.');
        }

        // Check if lesson belongs to the course
        if ($lesson->course_id !== $course->id) {
            abort(404);
        }

        // Check if lesson is published
        if (!$lesson->is_published) {
            abort(404);
        }

        // Load course lessons for navigation
        $course->load(['lessons' => function($query) {
            $query->where('is_published', true)->orderBy('sort_order');
        }]);

        // Get user's progress for all lessons in this course
        $lessonProgress = $user->lessonProgress()
            ->whereIn('lesson_id', $course->lessons->pluck('id'))
            ->get()
            ->keyBy('lesson_id');

        // Get or create progress for current lesson
        $currentProgress = $lessonProgress->get($lesson->id);
        if (!$currentProgress) {
            $currentProgress = LessonProgress::create([
                'user_id' => $user->id,
                'lesson_id' => $lesson->id,
                'started_at' => now(),
            ]);
        }

        // Update last accessed time
        $currentProgress->update(['last_accessed_at' => now()]);

        // Find previous and next lessons
        $lessonIndex = $course->lessons->search(function($l) use ($lesson) {
            return $l->id === $lesson->id;
        });

        $previousLesson = $lessonIndex > 0 ? $course->lessons[$lessonIndex - 1] : null;
        $nextLesson = $lessonIndex < $course->lessons->count() - 1 ? $course->lessons[$lessonIndex + 1] : null;

        return view('lessons.show', compact(
            'course',
            'lesson',
            'enrollment',
            'lessonProgress',
            'currentProgress',
            'previousLesson',
            'nextLesson'
        ));
    }

    /**
     * Mark lesson as completed.
     */
    public function complete(Request $request, Course $course, Lesson $lesson)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user = Auth::user();

        // Check enrollment
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();
        if (!$enrollment) {
            return response()->json(['error' => 'Not enrolled'], 403);
        }

        // Get or create lesson progress
        $progress = LessonProgress::firstOrCreate([
            'user_id' => $user->id,
            'lesson_id' => $lesson->id,
        ], [
            'started_at' => now(),
        ]);

        // Mark as completed
        $progress->update([
            'is_completed' => true,
            'completion_percentage' => 100,
            'completed_at' => now(),
        ]);

        // Update course enrollment progress
        $this->updateCourseProgress($enrollment);

        return response()->json([
            'success' => true,
            'message' => 'Lesson marked as completed!',
            'course_progress' => $enrollment->fresh()->progress_percentage
        ]);
    }

    /**
     * Update watch time for a lesson.
     */
    public function updateWatchTime(Request $request, Course $course, Lesson $lesson)
    {
        $request->validate([
            'watch_time' => 'required|integer|min:0',
            'completion_percentage' => 'required|numeric|min:0|max:100',
        ]);

        if (!Auth::check()) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $user = Auth::user();

        // Check enrollment
        $enrollment = $user->enrollments()->where('course_id', $course->id)->first();
        if (!$enrollment) {
            return response()->json(['error' => 'Not enrolled'], 403);
        }

        // Get or create lesson progress
        $progress = LessonProgress::firstOrCreate([
            'user_id' => $user->id,
            'lesson_id' => $lesson->id,
        ], [
            'started_at' => now(),
        ]);

        // Update progress
        $progress->update([
            'watch_time_seconds' => $request->watch_time,
            'completion_percentage' => $request->completion_percentage,
            'last_accessed_at' => now(),
        ]);

        // Auto-complete if 90% or more watched
        if ($request->completion_percentage >= 90 && !$progress->is_completed) {
            $progress->update([
                'is_completed' => true,
                'completed_at' => now(),
            ]);

            // Update course progress
            $this->updateCourseProgress($enrollment);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Update overall course progress based on lesson completions.
     */
    private function updateCourseProgress($enrollment)
    {
        $course = $enrollment->course;
        $totalLessons = $course->lessons()->where('is_published', true)->count();

        if ($totalLessons === 0) {
            return;
        }

        $completedLessons = LessonProgress::where('user_id', $enrollment->user_id)
            ->whereIn('lesson_id', $course->lessons()->where('is_published', true)->pluck('id'))
            ->where('is_completed', true)
            ->count();

        $progressPercentage = round(($completedLessons / $totalLessons) * 100, 2);

        $enrollment->update([
            'progress_percentage' => $progressPercentage,
            'last_accessed_at' => now(),
        ]);

        // Mark course as completed if all lessons are done
        if ($progressPercentage >= 100) {
            $enrollment->update([
                'status' => 'completed',
                'completed_at' => now(),
            ]);
        }
    }
}
