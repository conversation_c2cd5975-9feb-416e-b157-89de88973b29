<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\LiveEvent;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
    }

    /**
     * Show the user dashboard.
     */
    public function index()
    {
        $user = Auth::user();

        // Get user's enrolled courses
        $enrolledCourses = $user->enrollments()
            ->with(['course.instructor', 'course.category'])
            ->latest()
            ->limit(6)
            ->get();

        // Get user's course progress
        $totalEnrolledCourses = $user->enrollments()->count();
        $completedCourses = $user->enrollments()->where('status', 'completed')->count();
        $progressPercentage = $totalEnrolledCourses > 0 ?
            round(($completedCourses / $totalEnrolledCourses) * 100, 1) : 0;

        // Get upcoming live events
        $upcomingEvents = LiveEvent::where('scheduled_at', '>', now())
            ->where('status', 'scheduled')
            ->orderBy('scheduled_at')
            ->limit(3)
            ->get();

        // Get recent notifications
        $notifications = $user->notifications()
            ->latest()
            ->limit(5)
            ->get();

        // Get recommended courses (not enrolled)
        $recommendedCourses = Course::published()
            ->whereNotIn('id', $user->courses()->pluck('courses.id'))
            ->inRandomOrder()
            ->limit(4)
            ->get();

        return view('dashboard.index', compact(
            'user',
            'enrolledCourses',
            'totalEnrolledCourses',
            'completedCourses',
            'progressPercentage',
            'upcomingEvents',
            'notifications',
            'recommendedCourses'
        ));
    }

    /**
     * Show user's courses.
     */
    public function courses()
    {
        $user = Auth::user();

        $enrollments = $user->enrollments()
            ->with(['course.instructor', 'course.category'])
            ->paginate(12);

        return view('dashboard.courses', compact('enrollments'));
    }

    /**
     * Show user's profile.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('dashboard.profile', compact('user'));
    }

    /**
     * Update user's profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'timezone' => 'required|string',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only(['first_name', 'last_name', 'email', 'phone', 'bio', 'timezone']);

        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $data['avatar'] = $avatarPath;
        }

        $user->update($data);

        return redirect()->back()->with('success', 'Profile updated successfully!');
    }
}
