<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\LiveEvent;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
    }

    /**
     * Show the user dashboard.
     */
    public function index()
    {
        $user = Auth::user();

        // Get user's enrolled courses
        $enrolledCourses = $user->enrollments()
            ->with(['course.instructor', 'course.category'])
            ->latest()
            ->limit(6)
            ->get();

        // Get user's course progress
        $totalEnrolledCourses = $user->enrollments()->count();
        $completedCourses = $user->enrollments()->where('status', 'completed')->count();
        $progressPercentage = $totalEnrolledCourses > 0 ?
            round(($completedCourses / $totalEnrolledCourses) * 100, 1) : 0;

        // Get upcoming live events
        $upcomingEvents = LiveEvent::where('scheduled_at', '>', now())
            ->where('status', 'scheduled')
            ->orderBy('scheduled_at')
            ->limit(3)
            ->get();

        // Get recent notifications
        $notifications = $user->notifications()
            ->latest()
            ->limit(5)
            ->get();

        // Get recommended courses (not enrolled)
        $recommendedCourses = Course::published()
            ->whereNotIn('id', $user->courses()->pluck('courses.id'))
            ->inRandomOrder()
            ->limit(4)
            ->get();

        // Get learning streak (days in a row with activity)
        $learningStreak = $this->calculateLearningStreak($user);

        // Get total watch time
        $totalWatchTime = $user->lessonProgress()->sum('watch_time_seconds');

        // Get achievements/badges
        $achievements = $this->getUserAchievements($user);

        return view('dashboard.index', compact(
            'user',
            'enrolledCourses',
            'totalEnrolledCourses',
            'completedCourses',
            'progressPercentage',
            'upcomingEvents',
            'notifications',
            'recommendedCourses',
            'learningStreak',
            'totalWatchTime',
            'achievements'
        ));
    }

    /**
     * Calculate user's learning streak.
     */
    private function calculateLearningStreak($user)
    {
        $streak = 0;
        $currentDate = now()->startOfDay();

        while (true) {
            $hasActivity = $user->lessonProgress()
                ->whereDate('last_accessed_at', $currentDate)
                ->exists();

            if (!$hasActivity) {
                break;
            }

            $streak++;
            $currentDate->subDay();
        }

        return $streak;
    }

    /**
     * Get user achievements/badges.
     */
    private function getUserAchievements($user)
    {
        $achievements = [];

        // First Course Completed
        if ($user->enrollments()->where('status', 'completed')->count() >= 1) {
            $achievements[] = [
                'name' => 'First Course Completed',
                'icon' => 'fas fa-trophy',
                'color' => '#f39c12',
                'description' => 'Completed your first course'
            ];
        }

        // Course Collector
        if ($user->enrollments()->count() >= 5) {
            $achievements[] = [
                'name' => 'Course Collector',
                'icon' => 'fas fa-book-open',
                'color' => '#3498db',
                'description' => 'Enrolled in 5+ courses'
            ];
        }

        // Learning Streak
        $streak = $this->calculateLearningStreak($user);
        if ($streak >= 7) {
            $achievements[] = [
                'name' => 'Week Warrior',
                'icon' => 'fas fa-fire',
                'color' => '#e74c3c',
                'description' => '7-day learning streak'
            ];
        }

        // Watch Time
        $totalMinutes = $user->lessonProgress()->sum('watch_time_seconds') / 60;
        if ($totalMinutes >= 600) { // 10 hours
            $achievements[] = [
                'name' => 'Dedicated Learner',
                'icon' => 'fas fa-clock',
                'color' => '#9b59b6',
                'description' => '10+ hours of learning'
            ];
        }

        return $achievements;
    }

    /**
     * Show user's courses.
     */
    public function courses(Request $request)
    {
        $user = Auth::user();

        $query = $user->enrollments()
            ->with(['course.instructor', 'course.category']);

        // Filter by status
        if ($request->has('filter')) {
            switch ($request->filter) {
                case 'active':
                    $query->where('status', 'active');
                    break;
                case 'completed':
                    $query->where('status', 'completed');
                    break;
                case 'suspended':
                    $query->where('status', 'suspended');
                    break;
            }
        }

        // Sort by most recent activity
        $query->orderBy('last_accessed_at', 'desc')
              ->orderBy('created_at', 'desc');

        $enrollments = $query->paginate(12);

        return view('dashboard.courses', compact('enrollments'));
    }

    /**
     * Show user's profile.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('dashboard.profile', compact('user'));
    }

    /**
     * Update user's profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'bio' => 'nullable|string|max:1000',
            'timezone' => 'required|string',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only(['first_name', 'last_name', 'email', 'phone', 'bio', 'timezone']);

        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $data['avatar'] = $avatarPath;
        }

        $user->update($data);

        return redirect()->back()->with('success', 'Profile updated successfully!');
    }
}
