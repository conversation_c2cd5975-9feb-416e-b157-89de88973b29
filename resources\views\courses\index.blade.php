@extends('layouts.app')

@section('title', 'Courses')

@section('content')
<style>
    .courses-hero {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
    }

    .courses-hero h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        font-weight: bold;
    }

    .courses-hero p {
        font-size: 1.1rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
    }

    .courses-container {
        padding: 3rem 0;
    }

    .filters-section {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
    }

    .filter-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: var(--primary-color);
    }

    .filter-select,
    .filter-input {
        padding: 0.75rem;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        font-size: 1rem;
        transition: border-color 0.3s;
    }

    .filter-select:focus,
    .filter-input:focus {
        outline: none;
        border-color: var(--secondary-color);
    }

    .btn-filter {
        padding: 0.75rem 1.5rem;
        background: var(--secondary-color);
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 500;
        transition: background 0.3s;
    }

    .btn-filter:hover {
        background: #e67e22;
    }

    .courses-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .courses-count {
        color: #666;
        font-size: 1.1rem;
    }

    .sort-dropdown {
        padding: 0.5rem 1rem;
        border: 2px solid #e1e5e9;
        border-radius: 5px;
        background: white;
        cursor: pointer;
    }

    .courses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .course-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .course-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .course-thumbnail {
        width: 100%;
        height: 200px;
        background: linear-gradient(135deg, var(--secondary-color), #e67e22);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 3rem;
        position: relative;
    }

    .course-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .course-badge.free {
        background: #28a745;
    }

    .course-badge.featured {
        background: var(--accent-color);
    }

    .course-content {
        padding: 1.5rem;
    }

    .course-category {
        color: var(--secondary-color);
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .course-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: var(--primary-color);
        margin-bottom: 0.75rem;
        line-height: 1.3;
    }

    .course-description {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .course-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        color: #666;
    }

    .course-meta span {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .course-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .course-price {
        font-size: 1.2rem;
        font-weight: bold;
        color: var(--primary-color);
    }

    .course-price.free {
        color: #28a745;
    }

    .btn-course {
        padding: 0.5rem 1rem;
        background: var(--secondary-color);
        color: white;
        text-decoration: none;
        border-radius: 5px;
        font-weight: 500;
        transition: background 0.3s;
    }

    .btn-course:hover {
        background: #e67e22;
        color: white;
    }

    .no-courses {
        text-align: center;
        padding: 4rem 2rem;
        color: #666;
    }

    .no-courses i {
        font-size: 4rem;
        color: #ccc;
        margin-bottom: 1rem;
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }

    @media (max-width: 768px) {
        .courses-grid {
            grid-template-columns: 1fr;
        }
        
        .filters-grid {
            grid-template-columns: 1fr;
        }
        
        .courses-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
    }
</style>

<!-- Hero Section -->
<section class="courses-hero">
    <div class="container">
        <h1>Master New Skills</h1>
        <p>Learn from successful entrepreneurs and industry experts. Choose from our comprehensive library of courses designed to help you build wealth and achieve financial freedom.</p>
    </div>
</section>

<!-- Courses Section -->
<section class="courses-container">
    <div class="container">
        <!-- Filters -->
        <div class="filters-section">
            <form method="GET" action="{{ route('courses.index') }}">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="filter-label">Search Courses</label>
                        <input type="text" name="search" class="filter-input" placeholder="Search by title or description..." value="{{ request('search') }}">
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">Category</label>
                        <select name="category" class="filter-select">
                            <option value="">All Categories</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">Difficulty</label>
                        <select name="difficulty" class="filter-select">
                            <option value="">All Levels</option>
                            <option value="beginner" {{ request('difficulty') == 'beginner' ? 'selected' : '' }}>Beginner</option>
                            <option value="intermediate" {{ request('difficulty') == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                            <option value="advanced" {{ request('difficulty') == 'advanced' ? 'selected' : '' }}>Advanced</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="filter-label">Price</label>
                        <select name="price_filter" class="filter-select">
                            <option value="">All Courses</option>
                            <option value="free" {{ request('price_filter') == 'free' ? 'selected' : '' }}>Free</option>
                            <option value="paid" {{ request('price_filter') == 'paid' ? 'selected' : '' }}>Paid</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <button type="submit" class="btn-filter">
                            <i class="fas fa-search"></i> Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Courses Header -->
        <div class="courses-header">
            <div class="courses-count">
                {{ $courses->total() }} course{{ $courses->total() !== 1 ? 's' : '' }} found
            </div>
            
            <form method="GET" action="{{ route('courses.index') }}" style="display: inline;">
                @foreach(request()->except('sort') as $key => $value)
                    <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                @endforeach
                <select name="sort" class="sort-dropdown" onchange="this.form.submit()">
                    <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Latest</option>
                    <option value="popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Most Popular</option>
                    <option value="alphabetical" {{ request('sort') == 'alphabetical' ? 'selected' : '' }}>A-Z</option>
                    <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                    <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                </select>
            </form>
        </div>

        <!-- Courses Grid -->
        @if($courses->count() > 0)
            <div class="courses-grid">
                @foreach($courses as $course)
                <div class="course-card">
                    <div class="course-thumbnail">
                        <i class="fas fa-play-circle"></i>
                        @if($course->is_free)
                            <div class="course-badge free">FREE</div>
                        @endif
                        @if($course->is_featured)
                            <div class="course-badge featured">FEATURED</div>
                        @endif
                    </div>
                    
                    <div class="course-content">
                        <div class="course-category">{{ $course->category->name }}</div>
                        <h3 class="course-title">{{ $course->title }}</h3>
                        <p class="course-description">{{ $course->short_description }}</p>
                        
                        <div class="course-meta">
                            <span><i class="fas fa-clock"></i> {{ $course->duration_minutes }}min</span>
                            <span><i class="fas fa-signal"></i> {{ ucfirst($course->difficulty_level) }}</span>
                            <span><i class="fas fa-user"></i> {{ $course->instructor->full_name }}</span>
                        </div>
                        
                        <div class="course-footer">
                            <div class="course-price {{ $course->is_free ? 'free' : '' }}">
                                @if($course->is_free)
                                    FREE
                                @else
                                    ${{ number_format($course->price, 0) }}
                                @endif
                            </div>
                            <a href="{{ route('courses.show', $course) }}" class="btn-course">
                                View Course
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper">
                {{ $courses->appends(request()->query())->links() }}
            </div>
        @else
            <div class="no-courses">
                <i class="fas fa-search"></i>
                <h3>No courses found</h3>
                <p>Try adjusting your search criteria or browse all courses.</p>
                <a href="{{ route('courses.index') }}" class="btn-course">View All Courses</a>
            </div>
        @endif
    </div>
</section>
@endsection
